using System;
using System.Threading.Tasks;
using BCrypt.Net;
using Microsoft.Extensions.Logging;
using MobileShopManagement.Models;
using MobileShopManagement.DataAccess.Repositories;

namespace MobileShopManagement.BusinessLogic
{
    /// <summary>
    /// خدمة المصادقة - Authentication Service
    /// تدير عمليات تسجيل الدخول والمصادقة وإدارة كلمات المرور
    /// </summary>
    public class AuthenticationService
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<AuthenticationService> _logger;
        private User? _currentUser;

        /// <summary>
        /// المستخدم الحالي - Current User
        /// </summary>
        public User? CurrentUser => _currentUser;

        /// <summary>
        /// التحقق من تسجيل الدخول - Check if Logged In
        /// </summary>
        public bool IsLoggedIn => _currentUser != null;

        /// <summary>
        /// منشئ خدمة المصادقة - Authentication Service Constructor
        /// </summary>
        /// <param name="userRepository">مستودع المستخدمين</param>
        /// <param name="logger">مسجل الأحداث</param>
        public AuthenticationService(IUserRepository userRepository, ILogger<AuthenticationService> logger)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// تسجيل الدخول - Login
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة تسجيل الدخول</returns>
        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    _logger.LogWarning("محاولة تسجيل دخول بمعلومات فارغة");
                    return new LoginResult { Success = false, Message = "اسم المستخدم وكلمة المرور مطلوبان" };
                }

                // البحث عن المستخدم
                var user = await _userRepository.GetUserByUsernameAsync(username);
                if (user == null)
                {
                    _logger.LogWarning("محاولة تسجيل دخول بمستخدم غير موجود: {Username}", username);
                    return new LoginResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }

                // التحقق من حالة المستخدم
                if (!user.IsActive)
                {
                    _logger.LogWarning("محاولة تسجيل دخول بمستخدم غير نشط: {Username}", username);
                    return new LoginResult { Success = false, Message = "حساب المستخدم غير نشط" };
                }

                // التحقق من كلمة المرور
                if (!VerifyPassword(password, user.PasswordHash, user.Salt))
                {
                    _logger.LogWarning("محاولة تسجيل دخول بكلمة مرور خاطئة: {Username}", username);
                    return new LoginResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }

                // تحديث تاريخ آخر تسجيل دخول
                await _userRepository.UpdateLastLoginAsync(user.UserID);
                user.UpdateLastLogin();

                // تعيين المستخدم الحالي
                _currentUser = user;

                _logger.LogInformation("تم تسجيل الدخول بنجاح: {Username}, الدور: {Role}", username, user.Role);
                return new LoginResult 
                { 
                    Success = true, 
                    Message = "تم تسجيل الدخول بنجاح", 
                    User = user 
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الدخول: {Username}", username);
                return new LoginResult { Success = false, Message = "حدث خطأ أثناء تسجيل الدخول" };
            }
        }

        /// <summary>
        /// تسجيل الخروج - Logout
        /// </summary>
        public void Logout()
        {
            if (_currentUser != null)
            {
                _logger.LogInformation("تم تسجيل الخروج: {Username}", _currentUser.Username);
                _currentUser = null;
            }
        }

        /// <summary>
        /// تغيير كلمة المرور - Change Password
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="currentPassword">كلمة المرور الحالية</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>نتيجة تغيير كلمة المرور</returns>
        public async Task<ChangePasswordResult> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(currentPassword) || string.IsNullOrWhiteSpace(newPassword))
                {
                    return new ChangePasswordResult { Success = false, Message = "كلمة المرور الحالية والجديدة مطلوبتان" };
                }

                // التحقق من قوة كلمة المرور الجديدة
                var passwordValidation = ValidatePassword(newPassword);
                if (!passwordValidation.IsValid)
                {
                    return new ChangePasswordResult { Success = false, Message = passwordValidation.Message };
                }

                // الحصول على المستخدم
                var user = await _userRepository.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return new ChangePasswordResult { Success = false, Message = "المستخدم غير موجود" };
                }

                // التحقق من كلمة المرور الحالية
                if (!VerifyPassword(currentPassword, user.PasswordHash, user.Salt))
                {
                    return new ChangePasswordResult { Success = false, Message = "كلمة المرور الحالية غير صحيحة" };
                }

                // تشفير كلمة المرور الجديدة
                var (hashedPassword, salt) = HashPassword(newPassword);

                // تحديث كلمة المرور في قاعدة البيانات
                var success = await _userRepository.ChangePasswordAsync(userId, hashedPassword, salt);
                if (success)
                {
                    _logger.LogInformation("تم تغيير كلمة المرور بنجاح للمستخدم: {UserId}", userId);
                    return new ChangePasswordResult { Success = true, Message = "تم تغيير كلمة المرور بنجاح" };
                }
                else
                {
                    return new ChangePasswordResult { Success = false, Message = "فشل في تغيير كلمة المرور" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة المرور للمستخدم: {UserId}", userId);
                return new ChangePasswordResult { Success = false, Message = "حدث خطأ أثناء تغيير كلمة المرور" };
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد - Create New User
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="role">الدور</param>
        /// <returns>نتيجة إنشاء المستخدم</returns>
        public async Task<CreateUserResult> CreateUserAsync(string username, string password, string fullName, string role)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password) || 
                    string.IsNullOrWhiteSpace(fullName) || string.IsNullOrWhiteSpace(role))
                {
                    return new CreateUserResult { Success = false, Message = "جميع البيانات مطلوبة" };
                }

                // التحقق من عدم وجود اسم المستخدم
                var usernameExists = await _userRepository.UsernameExistsAsync(username);
                if (usernameExists)
                {
                    return new CreateUserResult { Success = false, Message = "اسم المستخدم موجود بالفعل" };
                }

                // التحقق من قوة كلمة المرور
                var passwordValidation = ValidatePassword(password);
                if (!passwordValidation.IsValid)
                {
                    return new CreateUserResult { Success = false, Message = passwordValidation.Message };
                }

                // تشفير كلمة المرور
                var (hashedPassword, salt) = HashPassword(password);

                // إنشاء المستخدم
                var user = new User
                {
                    Username = username,
                    PasswordHash = hashedPassword,
                    Salt = salt,
                    FullName = fullName,
                    Role = role,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var userId = await _userRepository.AddUserAsync(user);
                user.UserID = userId;

                _logger.LogInformation("تم إنشاء مستخدم جديد بنجاح: {Username}, المعرف: {UserId}", username, userId);
                return new CreateUserResult { Success = true, Message = "تم إنشاء المستخدم بنجاح", User = user };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء مستخدم جديد: {Username}", username);
                return new CreateUserResult { Success = false, Message = "حدث خطأ أثناء إنشاء المستخدم" };
            }
        }

        /// <summary>
        /// التحقق من الصلاحيات - Check Permissions
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم لديه الصلاحية</returns>
        public bool HasPermission(string requiredRole)
        {
            if (!IsLoggedIn || _currentUser == null)
                return false;

            return requiredRole switch
            {
                "Admin" => _currentUser.Role == "Admin",
                "Manager" => _currentUser.Role == "Admin" || _currentUser.Role == "Manager",
                "Employee" => _currentUser.Role == "Admin" || _currentUser.Role == "Manager" || _currentUser.Role == "Employee",
                _ => false
            };
        }

        /// <summary>
        /// تشفير كلمة المرور - Hash Password
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة والملح</returns>
        public static (string hashedPassword, string salt) HashPassword(string password)
        {
            var salt = BCrypt.Net.BCrypt.GenerateSalt(12);
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, salt);
            return (hashedPassword, salt);
        }

        /// <summary>
        /// التحقق من كلمة المرور - Verify Password
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة</param>
        /// <param name="salt">الملح</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string hashedPassword, string salt)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور - Validate Password Strength
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة التحقق</returns>
        public static PasswordValidationResult ValidatePassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور مطلوبة" };

            if (password.Length < 8)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور يجب أن تكون 8 أحرف على الأقل" };

            if (password.Length > 100)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور طويلة جداً" };

            bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;

            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsLower(c)) hasLower = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else if (!char.IsLetterOrDigit(c)) hasSpecial = true;
            }

            if (!hasUpper)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل" };

            if (!hasLower)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل" };

            if (!hasDigit)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل" };

            if (!hasSpecial)
                return new PasswordValidationResult { IsValid = false, Message = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل" };

            return new PasswordValidationResult { IsValid = true, Message = "كلمة المرور قوية" };
        }
    }

    /// <summary>
    /// نتيجة تسجيل الدخول - Login Result
    /// </summary>
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
    }

    /// <summary>
    /// نتيجة تغيير كلمة المرور - Change Password Result
    /// </summary>
    public class ChangePasswordResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة إنشاء المستخدم - Create User Result
    /// </summary>
    public class CreateUserResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
    }

    /// <summary>
    /// نتيجة التحقق من كلمة المرور - Password Validation Result
    /// </summary>
    public class PasswordValidationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
