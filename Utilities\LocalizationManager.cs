using System;
using System.Globalization;
using System.Resources;
using System.Threading;
using System.Windows.Forms;

namespace MobileShopManagement.Utilities
{
    /// <summary>
    /// مدير الترجمة والتوطين - Localization Manager
    /// يدير تغيير اللغة وتطبيق الترجمات على واجهة المستخدم
    /// </summary>
    public static class LocalizationManager
    {
        private static ResourceManager? _resourceManager;
        private static CultureInfo _currentCulture = new("ar-SA");
        
        /// <summary>
        /// اللغة الحالية - Current Culture
        /// </summary>
        public static CultureInfo CurrentCulture => _currentCulture;

        /// <summary>
        /// هل اللغة الحالية عربية - Is Current Language Arabic
        /// </summary>
        public static bool IsArabic => _currentCulture.Name.StartsWith("ar");

        /// <summary>
        /// تهيئة مدير الترجمة - Initialize Localization Manager
        /// </summary>
        static LocalizationManager()
        {
            _resourceManager = new ResourceManager("MobileShopManagement.Resources.Strings", 
                typeof(LocalizationManager).Assembly);
            SetCulture("ar-SA");
        }

        /// <summary>
        /// تعيين اللغة - Set Culture
        /// </summary>
        /// <param name="cultureName">اسم اللغة</param>
        public static void SetCulture(string cultureName)
        {
            try
            {
                _currentCulture = new CultureInfo(cultureName);
                Thread.CurrentThread.CurrentCulture = _currentCulture;
                Thread.CurrentThread.CurrentUICulture = _currentCulture;
                
                // تطبيق اتجاه النص
                if (IsArabic)
                {
                    Application.RightToLeft = RightToLeft.Yes;
                }
                else
                {
                    Application.RightToLeft = RightToLeft.No;
                }
            }
            catch (Exception)
            {
                // في حالة فشل تعيين اللغة، استخدم الافتراضية
                _currentCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = _currentCulture;
                Thread.CurrentThread.CurrentUICulture = _currentCulture;
            }
        }

        /// <summary>
        /// الحصول على النص المترجم - Get Localized String
        /// </summary>
        /// <param name="key">مفتاح النص</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>النص المترجم</returns>
        public static string GetString(string key, string? defaultValue = null)
        {
            try
            {
                if (_resourceManager == null)
                    return defaultValue ?? key;

                var value = _resourceManager.GetString(key, _currentCulture);
                return value ?? defaultValue ?? key;
            }
            catch
            {
                return defaultValue ?? key;
            }
        }

        /// <summary>
        /// تطبيق الترجمة على النموذج - Apply Localization to Form
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyLocalization(Form form)
        {
            if (form == null) return;

            try
            {
                // تطبيق اتجاه النص
                form.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;
                form.RightToLeftLayout = IsArabic;

                // تطبيق الخط المناسب
                if (IsArabic)
                {
                    form.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular);
                }
                else
                {
                    form.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular);
                }

                // تطبيق الترجمة على العناصر
                ApplyLocalizationToControls(form.Controls);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الترجمة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الترجمة على العناصر - Apply Localization to Controls
        /// </summary>
        /// <param name="controls">مجموعة العناصر</param>
        private static void ApplyLocalizationToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                // تطبيق اتجاه النص
                control.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;

                // تطبيق الخط
                if (IsArabic)
                {
                    control.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular);
                }

                // تطبيق الترجمة حسب نوع العنصر
                if (!string.IsNullOrEmpty(control.Tag?.ToString()))
                {
                    var key = control.Tag.ToString();
                    control.Text = GetString(key!, control.Text);
                }

                // تطبيق الترجمة على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyLocalizationToControls(control.Controls);
                }

                // تطبيق خصائص خاصة لبعض العناصر
                ApplySpecialProperties(control);
            }
        }

        /// <summary>
        /// تطبيق خصائص خاصة للعناصر - Apply Special Properties
        /// </summary>
        /// <param name="control">العنصر</param>
        private static void ApplySpecialProperties(Control control)
        {
            switch (control)
            {
                case DataGridView dataGridView:
                    dataGridView.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;
                    dataGridView.DefaultCellStyle.Alignment = IsArabic 
                        ? DataGridViewContentAlignment.MiddleRight 
                        : DataGridViewContentAlignment.MiddleLeft;
                    break;

                case TextBox textBox:
                    textBox.TextAlign = IsArabic ? HorizontalAlignment.Right : HorizontalAlignment.Left;
                    break;

                case ComboBox comboBox:
                    comboBox.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;
                    break;

                case MenuStrip menuStrip:
                    menuStrip.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;
                    break;

                case ToolStrip toolStrip:
                    toolStrip.RightToLeft = IsArabic ? RightToLeft.Yes : RightToLeft.No;
                    break;
            }
        }

        /// <summary>
        /// تبديل اللغة - Toggle Language
        /// </summary>
        public static void ToggleLanguage()
        {
            if (IsArabic)
            {
                SetCulture("en-US");
            }
            else
            {
                SetCulture("ar-SA");
            }
        }

        /// <summary>
        /// تنسيق التاريخ حسب اللغة - Format Date by Culture
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>التاريخ المنسق</returns>
        public static string FormatDate(DateTime date)
        {
            return date.ToString("d", _currentCulture);
        }

        /// <summary>
        /// تنسيق التاريخ والوقت حسب اللغة - Format DateTime by Culture
        /// </summary>
        /// <param name="dateTime">التاريخ والوقت</param>
        /// <returns>التاريخ والوقت المنسق</returns>
        public static string FormatDateTime(DateTime dateTime)
        {
            return dateTime.ToString("g", _currentCulture);
        }

        /// <summary>
        /// تنسيق العملة حسب اللغة - Format Currency by Culture
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ المنسق</returns>
        public static string FormatCurrency(decimal amount)
        {
            if (IsArabic)
            {
                return $"{amount:N2} ريال";
            }
            else
            {
                return amount.ToString("C", _currentCulture);
            }
        }

        /// <summary>
        /// تنسيق الرقم حسب اللغة - Format Number by Culture
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <returns>الرقم المنسق</returns>
        public static string FormatNumber(decimal number)
        {
            return number.ToString("N", _currentCulture);
        }

        /// <summary>
        /// الحصول على اسم اليوم بالعربية - Get Arabic Day Name
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>اسم اليوم بالعربية</returns>
        public static string GetArabicDayName(DateTime date)
        {
            var arabicCulture = new CultureInfo("ar-SA");
            return date.ToString("dddd", arabicCulture);
        }

        /// <summary>
        /// الحصول على اسم الشهر بالعربية - Get Arabic Month Name
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>اسم الشهر بالعربية</returns>
        public static string GetArabicMonthName(DateTime date)
        {
            var arabicCulture = new CultureInfo("ar-SA");
            return date.ToString("MMMM", arabicCulture);
        }

        /// <summary>
        /// تحويل الأرقام إلى العربية - Convert Numbers to Arabic
        /// </summary>
        /// <param name="input">النص المدخل</param>
        /// <returns>النص مع الأرقام العربية</returns>
        public static string ConvertToArabicNumbers(string input)
        {
            if (string.IsNullOrEmpty(input) || !IsArabic)
                return input;

            var arabicNumbers = new[] { "٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩" };
            var result = input;

            for (int i = 0; i < 10; i++)
            {
                result = result.Replace(i.ToString(), arabicNumbers[i]);
            }

            return result;
        }

        /// <summary>
        /// تحويل الأرقام إلى الإنجليزية - Convert Numbers to English
        /// </summary>
        /// <param name="input">النص المدخل</param>
        /// <returns>النص مع الأرقام الإنجليزية</returns>
        public static string ConvertToEnglishNumbers(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var arabicNumbers = new[] { "٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩" };
            var result = input;

            for (int i = 0; i < 10; i++)
            {
                result = result.Replace(arabicNumbers[i], i.ToString());
            }

            return result;
        }
    }
}
