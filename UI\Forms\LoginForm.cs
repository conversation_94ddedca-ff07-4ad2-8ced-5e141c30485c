using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Utilities;

namespace MobileShopManagement.UI.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول - Login Form
    /// يوفر واجهة تسجيل الدخول للنظام
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authService;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private CheckBox chkRememberMe;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblTitle;
        private PictureBox picLogo;
        private Panel pnlMain;
        private Button btnLanguage;

        /// <summary>
        /// نتيجة تسجيل الدخول - Login Result
        /// </summary>
        public bool LoginSuccessful { get; private set; }

        /// <summary>
        /// منشئ نموذج تسجيل الدخول - Login Form Constructor
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        public LoginForm(AuthenticationService authService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            InitializeComponent();
            InitializeLocalization();
        }

        /// <summary>
        /// تهيئة مكونات النموذج - Initialize Components
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج الأساسية
            this.Text = "تسجيل الدخول - Mobile Shop Management";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Font = new Font("Tahoma", 9F, FontStyle.Regular);

            // اللوحة الرئيسية
            pnlMain = new Panel
            {
                Size = new Size(400, 280),
                Location = new Point(25, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // شعار التطبيق
            picLogo = new PictureBox
            {
                Size = new Size(64, 64),
                Location = new Point(168, 20),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.LightBlue
            };

            // عنوان التطبيق
            lblTitle = new Label
            {
                Text = "نظام إدارة محل الهواتف المحمولة",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                Size = new Size(350, 25),
                Location = new Point(25, 95),
                TextAlign = ContentAlignment.MiddleCenter,
                Tag = "MainTitle"
            };

            // تسمية اسم المستخدم
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(100, 20),
                Location = new Point(280, 140),
                TextAlign = ContentAlignment.MiddleRight,
                Tag = "Username"
            };

            // مربع نص اسم المستخدم
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(200, 23),
                Location = new Point(70, 140),
                TextAlign = HorizontalAlignment.Right,
                TabIndex = 0
            };

            // تسمية كلمة المرور
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(100, 20),
                Location = new Point(280, 175),
                TextAlign = ContentAlignment.MiddleRight,
                Tag = "Password"
            };

            // مربع نص كلمة المرور
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(200, 23),
                Location = new Point(70, 175),
                UseSystemPasswordChar = true,
                TextAlign = HorizontalAlignment.Right,
                TabIndex = 1
            };

            // مربع اختيار تذكرني
            chkRememberMe = new CheckBox
            {
                Text = "تذكرني",
                Font = new Font("Tahoma", 8F, FontStyle.Regular),
                Size = new Size(80, 20),
                Location = new Point(290, 205),
                CheckAlign = ContentAlignment.MiddleRight,
                TextAlign = ContentAlignment.MiddleRight,
                TabIndex = 2,
                Tag = "RememberMe"
            };

            // زر تسجيل الدخول
            btnLogin = new Button
            {
                Text = "دخول",
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                Size = new Size(80, 30),
                Location = new Point(190, 235),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TabIndex = 3,
                Tag = "LoginButton"
            };
            btnLogin.FlatAppearance.BorderSize = 0;

            // زر الإلغاء
            btnCancel = new Button
            {
                Text = "إلغاء",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(80, 30),
                Location = new Point(100, 235),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TabIndex = 4,
                Tag = "Cancel"
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            // زر تغيير اللغة
            btnLanguage = new Button
            {
                Text = "EN",
                Font = new Font("Tahoma", 8F, FontStyle.Bold),
                Size = new Size(35, 25),
                Location = new Point(350, 10),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TabIndex = 5
            };
            btnLanguage.FlatAppearance.BorderSize = 0;

            // إضافة العناصر إلى اللوحة الرئيسية
            pnlMain.Controls.AddRange(new Control[]
            {
                picLogo, lblTitle, lblUsername, txtUsername,
                lblPassword, txtPassword, chkRememberMe,
                btnLogin, btnCancel, btnLanguage
            });

            // إضافة اللوحة الرئيسية إلى النموذج
            this.Controls.Add(pnlMain);

            // ربط الأحداث
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            btnLanguage.Click += BtnLanguage_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            this.Load += LoginForm_Load;

            this.ResumeLayout(false);
        }

        /// <summary>
        /// تهيئة الترجمة - Initialize Localization
        /// </summary>
        private void InitializeLocalization()
        {
            LocalizationManager.ApplyLocalization(this);
        }

        /// <summary>
        /// حدث تحميل النموذج - Form Load Event
        /// </summary>
        private void LoginForm_Load(object? sender, EventArgs e)
        {
            txtUsername.Focus();
            
            // تحميل آخر اسم مستخدم محفوظ
            LoadRememberedCredentials();
        }

        /// <summary>
        /// حدث النقر على زر تسجيل الدخول - Login Button Click Event
        /// </summary>
        private async void BtnLogin_Click(object? sender, EventArgs e)
        {
            await PerformLogin();
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء - Cancel Button Click Event
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// حدث النقر على زر تغيير اللغة - Language Button Click Event
        /// </summary>
        private void BtnLanguage_Click(object? sender, EventArgs e)
        {
            LocalizationManager.ToggleLanguage();
            LocalizationManager.ApplyLocalization(this);
            
            // تحديث نص زر اللغة
            btnLanguage.Text = LocalizationManager.IsArabic ? "EN" : "ع";
        }

        /// <summary>
        /// حدث الضغط على مفتاح في مربع كلمة المرور - Password TextBox KeyPress Event
        /// </summary>
        private async void TxtPassword_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLogin();
            }
        }

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول - Perform Login
        /// </summary>
        private async Task PerformLogin()
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    ShowMessage(LocalizationManager.GetString("Username") + " " + 
                               LocalizationManager.GetString("Required", "مطلوب"), 
                               LocalizationManager.GetString("Warning"), MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowMessage(LocalizationManager.GetString("Password") + " " + 
                               LocalizationManager.GetString("Required", "مطلوب"), 
                               LocalizationManager.GetString("Warning"), MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء المعالجة
                SetControlsEnabled(false);
                btnLogin.Text = LocalizationManager.GetString("Processing", "جاري المعالجة...");

                // تنفيذ تسجيل الدخول
                var result = await _authService.LoginAsync(txtUsername.Text.Trim(), txtPassword.Text);

                if (result.Success)
                {
                    // حفظ بيانات المستخدم إذا اختار تذكرني
                    if (chkRememberMe.Checked)
                    {
                        SaveRememberedCredentials();
                    }
                    else
                    {
                        ClearRememberedCredentials();
                    }

                    LoginSuccessful = true;
                    ShowMessage(LocalizationManager.GetString("LoginSuccessful"), 
                               LocalizationManager.GetString("Success"), MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowMessage(result.Message, LocalizationManager.GetString("Error"), MessageBoxIcon.Error);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowMessage(LocalizationManager.GetString("LoginError", "حدث خطأ أثناء تسجيل الدخول") + 
                           Environment.NewLine + ex.Message, 
                           LocalizationManager.GetString("Error"), MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تفعيل الأزرار
                SetControlsEnabled(true);
                btnLogin.Text = LocalizationManager.GetString("LoginButton");
            }
        }

        /// <summary>
        /// تفعيل أو تعطيل العناصر - Enable or Disable Controls
        /// </summary>
        /// <param name="enabled">حالة التفعيل</param>
        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnCancel.Enabled = enabled;
            chkRememberMe.Enabled = enabled;
        }

        /// <summary>
        /// عرض رسالة - Show Message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="icon">الأيقونة</param>
        private void ShowMessage(string message, string title, MessageBoxIcon icon)
        {
            MessageBox.Show(this, message, title, MessageBoxButtons.OK, icon, 
                           MessageBoxDefaultButton.Button1, 
                           LocalizationManager.IsArabic ? MessageBoxOptions.RtlReading : 0);
        }

        /// <summary>
        /// تحميل بيانات المستخدم المحفوظة - Load Remembered Credentials
        /// </summary>
        private void LoadRememberedCredentials()
        {
            try
            {
                // يمكن تنفيذ هذا باستخدام Registry أو ملف إعدادات
                // هنا مثال بسيط
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// حفظ بيانات المستخدم - Save Remembered Credentials
        /// </summary>
        private void SaveRememberedCredentials()
        {
            try
            {
                // يمكن تنفيذ هذا باستخدام Registry أو ملف إعدادات
                // هنا مثال بسيط
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// مسح بيانات المستخدم المحفوظة - Clear Remembered Credentials
        /// </summary>
        private void ClearRememberedCredentials()
        {
            try
            {
                // يمكن تنفيذ هذا باستخدام Registry أو ملف إعدادات
                // هنا مثال بسيط
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
    }
}
