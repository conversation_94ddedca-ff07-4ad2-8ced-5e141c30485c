-- Mobile Phone Shop Management System Database Schema
-- Created for Arabic RTL Windows Forms Application
-- Database: MobileShopDB

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'MobileShopDB')
BEGIN
    CREATE DATABASE MobileShopDB
    COLLATE Arabic_CI_AS;
END
GO

USE MobileShopDB;
GO

-- Create Users table for authentication
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        PasswordHash nvarchar(255) NOT NULL,
        Salt nvarchar(255) NOT NULL,
        Role nvarchar(20) NOT NULL DEFAULT 'Employee',
        FullName nvarchar(100) NOT NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        LastLoginDate datetime2 NULL,
        CONSTRAINT CK_Users_Role CHECK (Role IN ('Admin', 'Manager', 'Employee'))
    );
END
GO

-- Create Inventory table for product management
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Inventory' AND xtype='U')
BEGIN
    CREATE TABLE Inventory (
        ProductID int IDENTITY(1,1) PRIMARY KEY,
        ProductName nvarchar(100) NOT NULL,
        ProductNameArabic nvarchar(100) NOT NULL,
        QuantityAvailable int NOT NULL DEFAULT 0,
        UnitCost decimal(10,2) NOT NULL,
        SellingPrice decimal(10,2) NOT NULL,
        Category nvarchar(50) NULL,
        CategoryArabic nvarchar(50) NULL,
        Brand nvarchar(50) NULL,
        Model nvarchar(50) NULL,
        MinStockLevel int NOT NULL DEFAULT 5,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        LastUpdated datetime2 NOT NULL DEFAULT GETDATE(),
        CONSTRAINT CK_Inventory_Quantity CHECK (QuantityAvailable >= 0),
        CONSTRAINT CK_Inventory_Cost CHECK (UnitCost >= 0),
        CONSTRAINT CK_Inventory_Price CHECK (SellingPrice >= 0)
    );
END
GO

-- Create Sales table for transaction records
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sales' AND xtype='U')
BEGIN
    CREATE TABLE Sales (
        SaleID int IDENTITY(1,1) PRIMARY KEY,
        ProductID int NOT NULL,
        ProductName nvarchar(100) NOT NULL,
        SalePrice decimal(10,2) NOT NULL,
        UnitCost decimal(10,2) NOT NULL,
        QuantitySold int NOT NULL,
        TotalAmount AS (SalePrice * QuantitySold) PERSISTED,
        Profit AS ((SalePrice - UnitCost) * QuantitySold) PERSISTED,
        CustomerName nvarchar(100) NULL,
        CustomerPhone nvarchar(20) NULL,
        SaleDate datetime2 NOT NULL DEFAULT GETDATE(),
        SaleDay AS (DATENAME(WEEKDAY, SaleDate)) PERSISTED,
        SoldBy int NOT NULL,
        PaymentMethod nvarchar(20) NOT NULL DEFAULT 'Cash',
        Notes nvarchar(500) NULL,
        CONSTRAINT FK_Sales_Product FOREIGN KEY (ProductID) REFERENCES Inventory(ProductID),
        CONSTRAINT FK_Sales_User FOREIGN KEY (SoldBy) REFERENCES Users(UserID),
        CONSTRAINT CK_Sales_Quantity CHECK (QuantitySold > 0),
        CONSTRAINT CK_Sales_Price CHECK (SalePrice >= 0),
        CONSTRAINT CK_Sales_Payment CHECK (PaymentMethod IN ('Cash', 'Card', 'Transfer', 'Installment'))
    );
END
GO

-- Create Customers table for customer management
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID int IDENTITY(1,1) PRIMARY KEY,
        CustomerName nvarchar(100) NOT NULL,
        Phone nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        Address nvarchar(200) NULL,
        TotalPurchases decimal(12,2) NOT NULL DEFAULT 0,
        LastPurchaseDate datetime2 NULL,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        IsActive bit NOT NULL DEFAULT 1
    );
END
GO

-- Create BackupLog table to track backup operations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BackupLog' AND xtype='U')
BEGIN
    CREATE TABLE BackupLog (
        BackupID int IDENTITY(1,1) PRIMARY KEY,
        BackupDate datetime2 NOT NULL DEFAULT GETDATE(),
        BackupPath nvarchar(500) NOT NULL,
        BackupSize bigint NULL,
        BackupType nvarchar(20) NOT NULL DEFAULT 'Manual',
        PerformedBy int NOT NULL,
        Status nvarchar(20) NOT NULL DEFAULT 'Success',
        ErrorMessage nvarchar(1000) NULL,
        CONSTRAINT FK_BackupLog_User FOREIGN KEY (PerformedBy) REFERENCES Users(UserID),
        CONSTRAINT CK_BackupLog_Type CHECK (BackupType IN ('Manual', 'Automatic', 'Scheduled')),
        CONSTRAINT CK_BackupLog_Status CHECK (Status IN ('Success', 'Failed', 'In Progress'))
    );
END
GO

-- Create indexes for better performance
CREATE NONCLUSTERED INDEX IX_Sales_Date ON Sales(SaleDate);
CREATE NONCLUSTERED INDEX IX_Sales_Product ON Sales(ProductID);
CREATE NONCLUSTERED INDEX IX_Sales_Customer ON Sales(CustomerName);
CREATE NONCLUSTERED INDEX IX_Inventory_Name ON Inventory(ProductName);
CREATE NONCLUSTERED INDEX IX_Inventory_Category ON Inventory(Category);
GO

-- Insert default admin user (password: Admin123!)
-- Note: In actual application, this will be hashed using BCrypt
INSERT INTO Users (Username, PasswordHash, Salt, Role, FullName)
VALUES ('admin', 'TEMP_HASH', 'TEMP_SALT', 'Admin', N'مدير النظام');
GO

-- Insert sample categories and products
INSERT INTO Inventory (ProductName, ProductNameArabic, QuantityAvailable, UnitCost, SellingPrice, Category, CategoryArabic, Brand, Model, MinStockLevel)
VALUES 
    ('iPhone 15 Pro', N'آيفون 15 برو', 10, 800.00, 1200.00, 'Smartphones', N'هواتف ذكية', 'Apple', 'iPhone 15 Pro', 3),
    ('Samsung Galaxy S24', N'سامسونج جالاكسي S24', 15, 600.00, 900.00, 'Smartphones', N'هواتف ذكية', 'Samsung', 'Galaxy S24', 5),
    ('Phone Case', N'غطاء هاتف', 50, 5.00, 15.00, 'Accessories', N'إكسسوارات', 'Generic', 'Universal', 10),
    ('Screen Protector', N'واقي شاشة', 100, 2.00, 8.00, 'Accessories', N'إكسسوارات', 'Generic', 'Tempered Glass', 20),
    ('Wireless Charger', N'شاحن لاسلكي', 25, 15.00, 35.00, 'Accessories', N'إكسسوارات', 'Generic', 'Qi Wireless', 5);
GO

PRINT N'Database schema created successfully - تم إنشاء قاعدة البيانات بنجاح';
