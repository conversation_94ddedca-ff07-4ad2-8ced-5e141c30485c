# نظام إدارة محل الهواتف المحمولة - Mobile Shop Management System

## نظرة عامة - Overview

نظام شامل لإدارة محل الهواتف المحمولة مبني باستخدام Windows Forms و .NET 6 مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL).

A comprehensive mobile phone shop management system built with Windows Forms and .NET 6, featuring full Arabic language support and RTL layout.

## المميزات الرئيسية - Key Features

### 🔐 نظام المصادقة والأمان - Authentication & Security
- تسجيل دخول آمن باستخدام BCrypt لتشفير كلمات المرور
- إدارة المستخدمين مع أدوار مختلفة (مدير، مدير عام، موظف)
- جلسات آمنة مع انتهاء صلاحية تلقائي
- تشفير قوي للبيانات الحساسة

### 🛒 إدارة المبيعات - Sales Management
- تسجيل مبيعات جديدة مع تحديث تلقائي للمخزون
- عرض وتصفية المبيعات حسب التاريخ والمنتج والعميل
- طباعة فواتير البيع
- تصدير بيانات المبيعات إلى Excel
- دعم طرق دفع متعددة (نقدي، بطاقة، تحويل، تقسيط)

### 📦 إدارة المخزون - Inventory Management
- إضافة وتعديل وحذف المنتجات
- تتبع الكميات المتاحة والحد الأدنى للمخزون
- تنبيهات المخزون المنخفض
- إدارة الفئات والعلامات التجارية
- حساب الأرباح وهوامش الربح

### 📊 التقارير والتحليلات - Reports & Analytics
- تقارير يومية وشهرية للمبيعات
- تحليل الأرباح والإيرادات
- المنتجات الأكثر مبيعاً
- تقارير المخزون والتكاليف
- تصدير التقارير إلى Excel وPDF

### 🌐 دعم اللغة العربية - Arabic Language Support
- واجهة مستخدم كاملة باللغة العربية
- اتجاه النص من اليمين إلى اليسار (RTL)
- إمكانية التبديل بين العربية والإنجليزية
- تنسيق التواريخ والأرقام حسب الثقافة العربية

### 💾 النسخ الاحتياطي - Backup System
- نسخ احتياطي يدوي وتلقائي لقاعدة البيانات
- ضغط ملفات النسخ الاحتياطي
- جدولة النسخ الاحتياطي التلقائي
- استعادة البيانات من النسخ الاحتياطية

## متطلبات النظام - System Requirements

### الحد الأدنى - Minimum Requirements
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- SQL Server 2019 أو SQL Server Express
- 4 GB RAM
- 500 MB مساحة تخزين

### الموصى به - Recommended
- Windows 11
- .NET 6.0 SDK
- SQL Server 2022
- 8 GB RAM
- 2 GB مساحة تخزين

## التثبيت والإعداد - Installation & Setup

### 1. إعداد قاعدة البيانات - Database Setup

```sql
-- تشغيل سكريبت إنشاء قاعدة البيانات
-- Run the database creation script
sqlcmd -S (localdb)\MSSQLLocalDB -i Database\CreateDatabase.sql
```

### 2. تكوين سلسلة الاتصال - Configure Connection String

قم بتحديث ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=MobileShopDB;Trusted_Connection=true;"
  }
}
```

### 3. بناء وتشغيل التطبيق - Build & Run

```bash
# استنساخ المشروع
git clone [repository-url]
cd MobileShopManagement

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

## بيانات الدخول الافتراضية - Default Login Credentials

- **اسم المستخدم**: admin
- **كلمة المرور**: Admin123!
- **الدور**: مدير عام

## هيكل المشروع - Project Structure

```
MobileShopManagement/
├── BusinessLogic/          # طبقة منطق الأعمال
│   ├── AuthenticationService.cs
│   ├── SalesService.cs
│   └── InventoryService.cs
├── DataAccess/            # طبقة الوصول للبيانات
│   ├── DatabaseConnection.cs
│   └── Repositories/
├── Models/                # نماذج البيانات
│   ├── User.cs
│   ├── Product.cs
│   └── Sale.cs
├── UI/                    # واجهة المستخدم
│   ├── Forms/
│   └── UserControls/
├── Resources/             # ملفات الترجمة
│   ├── Strings.resx
│   └── Strings.ar.resx
├── Utilities/             # الأدوات المساعدة
│   └── LocalizationManager.cs
└── Database/              # سكريبتات قاعدة البيانات
    └── CreateDatabase.sql
```

## الاستخدام - Usage

### تسجيل الدخول
1. قم بتشغيل التطبيق
2. أدخل اسم المستخدم وكلمة المرور
3. اختر "تذكرني" للحفظ التلقائي
4. انقر "دخول"

### إضافة منتج جديد
1. اذهب إلى قائمة "المخزون"
2. اختر "إضافة منتج"
3. املأ بيانات المنتج
4. حدد الحد الأدنى للمخزون
5. احفظ المنتج

### تسجيل بيع جديد
1. اذهب إلى قائمة "المبيعات"
2. اختر "بيع جديد"
3. اختر المنتج والكمية
4. أدخل بيانات العميل
5. اختر طريقة الدفع
6. احفظ البيع

### إنشاء تقرير
1. اذهب إلى قائمة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اعرض أو اطبع أو صدّر التقرير

## الأمان - Security

### تشفير كلمات المرور
- استخدام BCrypt مع salt عشوائي
- قوة تشفير 12 rounds
- عدم تخزين كلمات المرور بشكل واضح

### إدارة الجلسات
- انتهاء صلاحية تلقائي بعد 8 ساعات
- تسجيل خروج تلقائي عند عدم النشاط
- تتبع محاولات تسجيل الدخول الفاشلة

### صلاحيات المستخدمين
- **مدير عام**: جميع الصلاحيات
- **مدير**: المبيعات والمخزون والتقارير
- **موظف**: المبيعات فقط

## النسخ الاحتياطي - Backup

### النسخ اليدوي
1. اذهب إلى قائمة "النظام"
2. اختر "النسخ الاحتياطي"
3. حدد مجلد الحفظ
4. انقر "إنشاء نسخة احتياطية"

### النسخ التلقائي
- يتم تلقائياً كل 24 ساعة
- يحفظ في مجلد محدد مسبقاً
- يحتفظ بآخر 30 نسخة احتياطية

## استكشاف الأخطاء - Troubleshooting

### مشاكل الاتصال بقاعدة البيانات
1. تأكد من تشغيل SQL Server
2. تحقق من سلسلة الاتصال
3. تأكد من وجود قاعدة البيانات

### مشاكل الترجمة
1. تأكد من وجود ملفات الترجمة
2. أعد تشغيل التطبيق
3. تحقق من إعدادات اللغة

### مشاكل الأداء
1. تحقق من مساحة القرص الصلب
2. أعد فهرسة قاعدة البيانات
3. احذف الملفات المؤقتة

## المساهمة - Contributing

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## الترخيص - License

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم - Support

للحصول على الدعم:
- إنشاء Issue في GitHub
- مراسلة البريد الإلكتروني: <EMAIL>
- زيارة الوثائق: [docs.mobileshop.com]

## التحديثات المستقبلية - Future Updates

- [ ] تطبيق ويب متجاوب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام إدارة العملاء المتقدم
- [ ] تكامل مع الباركود
- [ ] نظام الإشعارات

---

**تم تطوير هذا النظام بواسطة فريق تطوير محترف مع التركيز على الأمان والأداء وسهولة الاستخدام.**
