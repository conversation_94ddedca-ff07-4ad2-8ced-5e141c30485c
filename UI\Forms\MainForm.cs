using System;
using System.Drawing;
using System.Windows.Forms;
using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Utilities;

namespace MobileShopManagement.UI.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق - Main Application Form
    /// يحتوي على القوائم الرئيسية والوصول لجميع وظائف النظام
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly AuthenticationService _authService;
        private MenuStrip menuMain;
        private ToolStrip toolMain;
        private StatusStrip statusMain;
        private Panel pnlContent;
        private Label lblWelcome;
        private Label lblCurrentUser;
        private ToolStripStatusLabel lblStatus;
        private ToolStripStatusLabel lblDateTime;

        /// <summary>
        /// منشئ النموذج الرئيسي - Main Form Constructor
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        public MainForm(AuthenticationService authService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            InitializeComponent();
            InitializeLocalization();
            SetupUserInterface();
        }

        /// <summary>
        /// تهيئة مكونات النموذج - Initialize Components
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج الأساسية
            this.Text = "نظام إدارة محل الهواتف المحمولة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Tahoma", 9F, FontStyle.Regular);
            this.Icon = SystemIcons.Application;

            // إنشاء القائمة الرئيسية
            CreateMainMenu();

            // إنشاء شريط الأدوات
            CreateToolbar();

            // إنشاء شريط الحالة
            CreateStatusBar();

            // إنشاء المحتوى الرئيسي
            CreateMainContent();

            // ترتيب العناصر
            this.Controls.Add(pnlContent);
            this.Controls.Add(toolMain);
            this.Controls.Add(statusMain);
            this.Controls.Add(menuMain);

            this.MainMenuStrip = menuMain;

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        /// <summary>
        /// إنشاء القائمة الرئيسية - Create Main Menu
        /// </summary>
        private void CreateMainMenu()
        {
            menuMain = new MenuStrip
            {
                BackColor = Color.FromArgb(52, 58, 64),
                ForeColor = Color.White,
                Font = new Font("Tahoma", 9F, FontStyle.Regular)
            };

            // قائمة المبيعات
            var menuSales = new ToolStripMenuItem("المبيعات") { Tag = "Sales" };
            menuSales.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("بيع جديد") { Tag = "NewSale" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("عرض المبيعات") { Tag = "ViewSales" },
                new ToolStripMenuItem("البحث في المبيعات") { Tag = "SearchSales" }
            });

            // قائمة المخزون
            var menuInventory = new ToolStripMenuItem("المخزون") { Tag = "Inventory" };
            menuInventory.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إضافة منتج") { Tag = "AddProduct" },
                new ToolStripMenuItem("تعديل منتج") { Tag = "EditProduct" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("عرض المخزون") { Tag = "ViewInventory" },
                new ToolStripMenuItem("تنبيهات المخزون") { Tag = "StockAlerts" }
            });

            // قائمة التقارير
            var menuReports = new ToolStripMenuItem("التقارير") { Tag = "Reports" };
            menuReports.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("تقرير يومي") { Tag = "DailyReport" },
                new ToolStripMenuItem("تقرير شهري") { Tag = "MonthlyReport" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("المنتجات الأكثر مبيعاً") { Tag = "TopSellingProducts" },
                new ToolStripMenuItem("تقرير الأرباح") { Tag = "ProfitReport" }
            });

            // قائمة المستخدمين (للمديرين فقط)
            var menuUsers = new ToolStripMenuItem("المستخدمون") { Tag = "Users" };
            menuUsers.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إضافة مستخدم") { Tag = "AddUser" },
                new ToolStripMenuItem("إدارة المستخدمين") { Tag = "ManageUsers" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("تغيير كلمة المرور") { Tag = "ChangePassword" }
            });

            // قائمة النظام
            var menuSystem = new ToolStripMenuItem("النظام") { Tag = "System" };
            menuSystem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("النسخ الاحتياطي") { Tag = "Backup" },
                new ToolStripMenuItem("الإعدادات") { Tag = "Settings" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("تغيير اللغة") { Tag = "ChangeLanguage" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("حول البرنامج") { Tag = "About" },
                new ToolStripMenuItem("تسجيل الخروج") { Tag = "Logout" }
            });

            // إضافة القوائم إلى القائمة الرئيسية
            menuMain.Items.AddRange(new ToolStripItem[]
            {
                menuSales, menuInventory, menuReports, menuUsers, menuSystem
            });
        }

        /// <summary>
        /// إنشاء شريط الأدوات - Create Toolbar
        /// </summary>
        private void CreateToolbar()
        {
            toolMain = new ToolStrip
            {
                BackColor = Color.FromArgb(233, 236, 239),
                GripStyle = ToolStripGripStyle.Hidden,
                ImageScalingSize = new Size(24, 24)
            };

            // أزرار شريط الأدوات
            var btnNewSale = new ToolStripButton("بيع جديد")
            {
                Tag = "NewSale",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };

            var btnInventory = new ToolStripButton("المخزون")
            {
                Tag = "Inventory",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };

            var btnReports = new ToolStripButton("التقارير")
            {
                Tag = "Reports",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };

            var btnBackup = new ToolStripButton("نسخ احتياطي")
            {
                Tag = "Backup",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };

            toolMain.Items.AddRange(new ToolStripItem[]
            {
                btnNewSale,
                new ToolStripSeparator(),
                btnInventory,
                new ToolStripSeparator(),
                btnReports,
                new ToolStripSeparator(),
                btnBackup
            });
        }

        /// <summary>
        /// إنشاء شريط الحالة - Create Status Bar
        /// </summary>
        private void CreateStatusBar()
        {
            statusMain = new StatusStrip
            {
                BackColor = Color.FromArgb(248, 249, 250)
            };

            lblStatus = new ToolStripStatusLabel("جاهز")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            lblDateTime = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd HH:mm"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            statusMain.Items.AddRange(new ToolStripItem[]
            {
                lblStatus, lblDateTime
            });

            // تحديث الوقت كل دقيقة
            var timer = new Timer { Interval = 60000 };
            timer.Tick += (s, e) => lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();
        }

        /// <summary>
        /// إنشاء المحتوى الرئيسي - Create Main Content
        /// </summary>
        private void CreateMainContent()
        {
            pnlContent = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // رسالة الترحيب
            lblWelcome = new Label
            {
                Text = "مرحباً بك في نظام إدارة محل الهواتف المحمولة",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                Size = new Size(600, 40),
                Location = new Point(50, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                Tag = "WelcomeMessage"
            };

            // معلومات المستخدم الحالي
            lblCurrentUser = new Label
            {
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Size = new Size(400, 25),
                Location = new Point(50, 100),
                TextAlign = ContentAlignment.MiddleCenter
            };

            pnlContent.Controls.AddRange(new Control[]
            {
                lblWelcome, lblCurrentUser
            });
        }

        /// <summary>
        /// تهيئة الترجمة - Initialize Localization
        /// </summary>
        private void InitializeLocalization()
        {
            LocalizationManager.ApplyLocalization(this);
        }

        /// <summary>
        /// إعداد واجهة المستخدم - Setup User Interface
        /// </summary>
        private void SetupUserInterface()
        {
            // عرض معلومات المستخدم الحالي
            if (_authService.CurrentUser != null)
            {
                lblCurrentUser.Text = $"المستخدم الحالي: {_authService.CurrentUser.FullName} ({_authService.CurrentUser.Role})";
            }

            // إخفاء قوائم المديرين للمستخدمين العاديين
            if (!_authService.HasPermission("Manager"))
            {
                // إخفاء قائمة المستخدمين
                if (menuMain.Items["Users"] is ToolStripMenuItem usersMenu)
                {
                    usersMenu.Visible = false;
                }
            }

            // تحديث حالة شريط الحالة
            lblStatus.Text = "جاهز - " + DateTime.Now.ToString("dddd، dd MMMM yyyy", 
                new System.Globalization.CultureInfo("ar-SA"));
        }

        /// <summary>
        /// حدث تحميل النموذج - Form Load Event
        /// </summary>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // التحقق من تنبيهات المخزون المنخفض
            CheckLowStockAlerts();
        }

        /// <summary>
        /// التحقق من تنبيهات المخزون المنخفض - Check Low Stock Alerts
        /// </summary>
        private void CheckLowStockAlerts()
        {
            // TODO: تنفيذ التحقق من المخزون المنخفض
            // يمكن إضافة هذا لاحقاً عند تنفيذ خدمة المخزون
        }

        /// <summary>
        /// حدث إغلاق النموذج - Form Closing Event
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من الخروج من النظام؟",
                "تأكيد الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2,
                LocalizationManager.IsArabic ? MessageBoxOptions.RtlReading : 0);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            // تسجيل الخروج
            _authService.Logout();
            
            base.OnFormClosing(e);
        }
    }
}
