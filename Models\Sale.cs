using System;
using System.ComponentModel.DataAnnotations;

namespace MobileShopManagement.Models
{
    /// <summary>
    /// نموذج المبيعات - Sales Model
    /// يمثل بيانات عمليات البيع
    /// </summary>
    public class Sale
    {
        /// <summary>
        /// معرف البيع الفريد - Unique Sale ID
        /// </summary>
        public int SaleID { get; set; }

        /// <summary>
        /// معرف المنتج - Product ID
        /// </summary>
        [Required(ErrorMessage = "معرف المنتج مطلوب")]
        public int ProductID { get; set; }

        /// <summary>
        /// اسم المنتج - Product Name
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 100 حرف")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// سعر البيع - Sale Price
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
        public decimal SalePrice { get; set; }

        /// <summary>
        /// تكلفة الوحدة - Unit Cost
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal UnitCost { get; set; }

        /// <summary>
        /// الكمية المباعة - Quantity Sold
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "الكمية المباعة يجب أن تكون أكبر من صفر")]
        public int QuantitySold { get; set; }

        /// <summary>
        /// المبلغ الإجمالي - Total Amount
        /// </summary>
        public decimal TotalAmount
        {
            get { return SalePrice * QuantitySold; }
        }

        /// <summary>
        /// الربح الإجمالي - Total Profit
        /// </summary>
        public decimal Profit
        {
            get { return (SalePrice - UnitCost) * QuantitySold; }
        }

        /// <summary>
        /// اسم العميل - Customer Name
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم العميل يجب أن يكون أقل من 100 حرف")]
        public string? CustomerName { get; set; }

        /// <summary>
        /// رقم هاتف العميل - Customer Phone
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// تاريخ البيع - Sale Date
        /// </summary>
        public DateTime SaleDate { get; set; } = DateTime.Now;

        /// <summary>
        /// يوم البيع - Sale Day
        /// </summary>
        public string SaleDay
        {
            get
            {
                return SaleDate.ToString("dddd", new System.Globalization.CultureInfo("ar-SA"));
            }
        }

        /// <summary>
        /// معرف البائع - Seller ID
        /// </summary>
        [Required(ErrorMessage = "معرف البائع مطلوب")]
        public int SoldBy { get; set; }

        /// <summary>
        /// اسم البائع - Seller Name
        /// </summary>
        public string? SellerName { get; set; }

        /// <summary>
        /// طريقة الدفع - Payment Method
        /// </summary>
        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        public string PaymentMethod { get; set; } = "Cash";

        /// <summary>
        /// ملاحظات - Notes
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// حساب هامش الربح - Calculate Profit Margin
        /// </summary>
        /// <returns>نسبة الربح كنسبة مئوية</returns>
        public decimal ProfitMargin
        {
            get
            {
                if (UnitCost == 0) return 0;
                return ((SalePrice - UnitCost) / UnitCost) * 100;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات - Validate Sale Data
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return ProductID > 0 && 
                   !string.IsNullOrWhiteSpace(ProductName) && 
                   SalePrice > 0 && 
                   QuantitySold > 0 && 
                   SoldBy > 0 &&
                   IsValidPaymentMethod();
        }

        /// <summary>
        /// التحقق من صحة طريقة الدفع - Validate Payment Method
        /// </summary>
        /// <returns>true إذا كانت طريقة الدفع صحيحة</returns>
        public bool IsValidPaymentMethod()
        {
            var validMethods = new[] { "Cash", "Card", "Transfer", "Installment" };
            return Array.Exists(validMethods, method => method == PaymentMethod);
        }

        /// <summary>
        /// إرجاع تمثيل نصي للبيع - String representation
        /// </summary>
        /// <returns>تفاصيل البيع</returns>
        public override string ToString()
        {
            return $"{ProductName} - {QuantitySold} × {SalePrice:C} = {TotalAmount:C}";
        }
    }

    /// <summary>
    /// طرق الدفع المتاحة - Available Payment Methods
    /// </summary>
    public enum PaymentMethod
    {
        /// <summary>نقدي - Cash</summary>
        Cash,
        /// <summary>بطاقة ائتمان - Credit Card</summary>
        Card,
        /// <summary>تحويل بنكي - Bank Transfer</summary>
        Transfer,
        /// <summary>تقسيط - Installment</summary>
        Installment
    }

    /// <summary>
    /// نموذج تقرير المبيعات - Sales Report Model
    /// </summary>
    public class SalesReport
    {
        /// <summary>
        /// تاريخ البداية - Start Date
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية - End Date
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// إجمالي المبيعات - Total Sales
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي الربح - Total Profit
        /// </summary>
        public decimal TotalProfit { get; set; }

        /// <summary>
        /// عدد العمليات - Number of Transactions
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// إجمالي الكمية المباعة - Total Quantity Sold
        /// </summary>
        public int TotalQuantitySold { get; set; }

        /// <summary>
        /// متوسط قيمة البيع - Average Sale Value
        /// </summary>
        public decimal AverageSaleValue
        {
            get
            {
                return TransactionCount > 0 ? TotalSales / TransactionCount : 0;
            }
        }

        /// <summary>
        /// نسبة الربح الإجمالية - Overall Profit Margin
        /// </summary>
        public decimal OverallProfitMargin
        {
            get
            {
                return TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
            }
        }
    }
}
