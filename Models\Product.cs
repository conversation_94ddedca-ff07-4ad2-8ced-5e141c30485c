using System;
using System.ComponentModel.DataAnnotations;

namespace MobileShopManagement.Models
{
    /// <summary>
    /// نموذج المنتج - Product Model
    /// يمثل بيانات المنتجات في المخزون
    /// </summary>
    public class Product
    {
        /// <summary>
        /// معرف المنتج الفريد - Unique Product ID
        /// </summary>
        public int ProductID { get; set; }

        /// <summary>
        /// اسم المنتج بالإنجليزية - Product Name in English
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 100 حرف")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// اسم المنتج بالعربية - Product Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج بالعربية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج بالعربية يجب أن يكون أقل من 100 حرف")]
        public string ProductNameArabic { get; set; } = string.Empty;

        /// <summary>
        /// الكمية المتاحة في المخزون - Available Quantity
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي صفر")]
        public int QuantityAvailable { get; set; }

        /// <summary>
        /// تكلفة الوحدة - Unit Cost
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal UnitCost { get; set; }

        /// <summary>
        /// سعر البيع - Selling Price
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو تساوي صفر")]
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// الفئة بالإنجليزية - Category in English
        /// </summary>
        [StringLength(50, ErrorMessage = "الفئة يجب أن تكون أقل من 50 حرف")]
        public string? Category { get; set; }

        /// <summary>
        /// الفئة بالعربية - Category in Arabic
        /// </summary>
        [StringLength(50, ErrorMessage = "الفئة بالعربية يجب أن تكون أقل من 50 حرف")]
        public string? CategoryArabic { get; set; }

        /// <summary>
        /// العلامة التجارية - Brand
        /// </summary>
        [StringLength(50, ErrorMessage = "العلامة التجارية يجب أن تكون أقل من 50 حرف")]
        public string? Brand { get; set; }

        /// <summary>
        /// الموديل - Model
        /// </summary>
        [StringLength(50, ErrorMessage = "الموديل يجب أن يكون أقل من 50 حرف")]
        public string? Model { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون - Minimum Stock Level
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public int MinStockLevel { get; set; } = 5;

        /// <summary>
        /// حالة المنتج (نشط/غير نشط) - Product Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إضافة المنتج - Product Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث - Last Update Date
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// حساب هامش الربح - Calculate Profit Margin
        /// </summary>
        /// <returns>نسبة الربح كنسبة مئوية</returns>
        public decimal ProfitMargin
        {
            get
            {
                if (UnitCost == 0) return 0;
                return ((SellingPrice - UnitCost) / UnitCost) * 100;
            }
        }

        /// <summary>
        /// حساب الربح لكل وحدة - Calculate Profit Per Unit
        /// </summary>
        /// <returns>الربح لكل وحدة</returns>
        public decimal ProfitPerUnit
        {
            get
            {
                return SellingPrice - UnitCost;
            }
        }

        /// <summary>
        /// التحقق من انخفاض المخزون - Check if stock is low
        /// </summary>
        /// <returns>true إذا كان المخزون منخفض</returns>
        public bool IsLowStock
        {
            get
            {
                return QuantityAvailable <= MinStockLevel;
            }
        }

        /// <summary>
        /// التحقق من نفاد المخزون - Check if out of stock
        /// </summary>
        /// <returns>true إذا نفد المخزون</returns>
        public bool IsOutOfStock
        {
            get
            {
                return QuantityAvailable == 0;
            }
        }

        /// <summary>
        /// حساب القيمة الإجمالية للمخزون - Calculate Total Stock Value
        /// </summary>
        /// <returns>القيمة الإجمالية بسعر التكلفة</returns>
        public decimal TotalStockValue
        {
            get
            {
                return QuantityAvailable * UnitCost;
            }
        }

        /// <summary>
        /// حساب القيمة الإجمالية للمخزون بسعر البيع - Calculate Total Stock Value at Selling Price
        /// </summary>
        /// <returns>القيمة الإجمالية بسعر البيع</returns>
        public decimal TotalStockValueAtSellingPrice
        {
            get
            {
                return QuantityAvailable * SellingPrice;
            }
        }

        /// <summary>
        /// تحديث الكمية المتاحة - Update Available Quantity
        /// </summary>
        /// <param name="quantity">الكمية الجديدة</param>
        public void UpdateQuantity(int quantity)
        {
            QuantityAvailable = Math.Max(0, quantity);
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// تقليل الكمية المتاحة - Reduce Available Quantity
        /// </summary>
        /// <param name="soldQuantity">الكمية المباعة</param>
        /// <returns>true إذا تم التقليل بنجاح</returns>
        public bool ReduceQuantity(int soldQuantity)
        {
            if (soldQuantity <= 0 || soldQuantity > QuantityAvailable)
                return false;

            QuantityAvailable -= soldQuantity;
            LastUpdated = DateTime.Now;
            return true;
        }

        /// <summary>
        /// إضافة كمية للمخزون - Add Quantity to Stock
        /// </summary>
        /// <param name="addedQuantity">الكمية المضافة</param>
        public void AddQuantity(int addedQuantity)
        {
            if (addedQuantity > 0)
            {
                QuantityAvailable += addedQuantity;
                LastUpdated = DateTime.Now;
            }
        }

        /// <summary>
        /// إرجاع تمثيل نصي للمنتج - String representation
        /// </summary>
        /// <returns>اسم المنتج والكمية المتاحة</returns>
        public override string ToString()
        {
            return $"{ProductNameArabic} - متاح: {QuantityAvailable}";
        }
    }
}
