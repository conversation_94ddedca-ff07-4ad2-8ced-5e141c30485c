using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using MobileShopManagement.BusinessLogic;
using MobileShopManagement.DataAccess;
using MobileShopManagement.DataAccess.Repositories;
using MobileShopManagement.UI.Forms;
using MobileShopManagement.Utilities;

namespace MobileShopManagement
{
    /// <summary>
    /// الفئة الرئيسية للتطبيق - Main Application Class
    /// نقطة دخول التطبيق وإعداد الخدمات
    /// </summary>
    internal static class Program
    {
        private static ServiceProvider? _serviceProvider;
        private static ILogger<object>? _logger;

        /// <summary>
        /// نقطة دخول التطبيق الرئيسية - Main Entry Point
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetHighDpiMode(HighDpiMode.SystemAware);

                // إعداد معالج الأخطاء العامة
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // تهيئة الخدمات
                ConfigureServices();

                // تهيئة نظام الترجمة
                LocalizationManager.SetCulture("ar-SA");

                // بدء التطبيق
                RunApplication();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // تنظيف الموارد
                _serviceProvider?.Dispose();
            }
        }

        /// <summary>
        /// إعداد الخدمات - Configure Services
        /// </summary>
        private static void ConfigureServices()
        {
            // إعداد التكوين
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // إعداد Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .WriteTo.File(
                    path: Path.Combine(configuration["AppSettings:LogPath"] ?? "Logs", "app-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    shared: true,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.Console()
                .CreateLogger();

            // إعداد حاوي الخدمات
            var services = new ServiceCollection();

            // إضافة التكوين
            services.AddSingleton<IConfiguration>(configuration);

            // إضافة نظام التسجيل
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // إضافة خدمات قاعدة البيانات
            services.AddSingleton<DatabaseConnection>();
            services.AddScoped<IUserRepository, UserRepository>();

            // إضافة خدمات الأعمال
            services.AddScoped<AuthenticationService>();

            // إضافة النماذج
            services.AddTransient<LoginForm>();
            services.AddTransient<MainForm>();

            // بناء مقدم الخدمات
            _serviceProvider = services.BuildServiceProvider();

            // الحصول على مسجل الأحداث
            _logger = _serviceProvider.GetService<ILogger<object>>();
            _logger?.LogInformation("تم تهيئة التطبيق بنجاح");
        }

        /// <summary>
        /// تشغيل التطبيق - Run Application
        /// </summary>
        private static void RunApplication()
        {
            try
            {
                // اختبار الاتصال بقاعدة البيانات
                var dbConnection = _serviceProvider?.GetService<DatabaseConnection>();
                if (dbConnection != null && !dbConnection.TestConnection())
                {
                    MessageBox.Show("فشل في الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.",
                                   "خطأ في قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // عرض نموذج تسجيل الدخول
                var authService = _serviceProvider?.GetService<AuthenticationService>();
                if (authService == null)
                {
                    MessageBox.Show("خطأ في تهيئة خدمة المصادقة", "خطأ", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                using var loginForm = new LoginForm(authService);
                if (loginForm.ShowDialog() == DialogResult.OK && loginForm.LoginSuccessful)
                {
                    _logger?.LogInformation("تم تسجيل الدخول بنجاح");

                    // عرض النموذج الرئيسي
                    var mainForm = _serviceProvider?.GetService<MainForm>();
                    if (mainForm != null)
                    {
                        Application.Run(mainForm);
                    }
                    else
                    {
                        MessageBox.Show("خطأ في تهيئة النموذج الرئيسي", "خطأ", 
                                       MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    _logger?.LogInformation("تم إلغاء تسجيل الدخول");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشغيل التطبيق");
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج الأخطاء في الخيط الرئيسي - Main Thread Exception Handler
        /// </summary>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            _logger?.LogError(e.Exception, "خطأ غير معالج في الخيط الرئيسي");
            
            var result = MessageBox.Show(
                $"حدث خطأ غير متوقع:{Environment.NewLine}{e.Exception.Message}{Environment.NewLine}{Environment.NewLine}" +
                "هل تريد المتابعة؟",
                "خطأ غير متوقع",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error);

            if (result == DialogResult.No)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// معالج الأخطاء في النطاق - Domain Exception Handler
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                _logger?.LogFatal(ex, "خطأ فادح غير معالج");
                
                MessageBox.Show(
                    $"حدث خطأ فادح:{Environment.NewLine}{ex.Message}{Environment.NewLine}{Environment.NewLine}" +
                    "سيتم إغلاق التطبيق.",
                    "خطأ فادح",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Stop);
            }

            // إغلاق التطبيق في حالة الأخطاء الفادحة
            Environment.Exit(1);
        }

        /// <summary>
        /// الحصول على خدمة - Get Service
        /// </summary>
        /// <typeparam name="T">نوع الخدمة</typeparam>
        /// <returns>الخدمة المطلوبة</returns>
        public static T? GetService<T>() where T : class
        {
            return _serviceProvider?.GetService<T>();
        }

        /// <summary>
        /// إنشاء نطاق خدمات - Create Service Scope
        /// </summary>
        /// <returns>نطاق الخدمات</returns>
        public static IServiceScope? CreateScope()
        {
            return _serviceProvider?.CreateScope();
        }
    }
}
