<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Login Form -->
  <data name="Login" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="LoginButton" xml:space="preserve">
    <value>دخول</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>تذكرني</value>
  </data>
  
  <!-- Main Form -->
  <data name="MainTitle" xml:space="preserve">
    <value>نظام إدارة محل الهواتف المحمولة</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>المبيعات</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>المخزون</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>المستخدمون</value>
  </data>
  <data name="Backup" xml:space="preserve">
    <value>النسخ الاحتياطي</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>تسجيل الخروج</value>
  </data>
  
  <!-- Sales Form -->
  <data name="NewSale" xml:space="preserve">
    <value>بيع جديد</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>اسم المنتج</value>
  </data>
  <data name="SalePrice" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="CustomerPhone" xml:space="preserve">
    <value>رقم هاتف العميل</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>طريقة الدفع</value>
  </data>
  <data name="Cash" xml:space="preserve">
    <value>نقدي</value>
  </data>
  <data name="Card" xml:space="preserve">
    <value>بطاقة</value>
  </data>
  <data name="Transfer" xml:space="preserve">
    <value>تحويل</value>
  </data>
  <data name="Installment" xml:space="preserve">
    <value>تقسيط</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>تصدير</value>
  </data>
  
  <!-- Inventory Form -->
  <data name="AddProduct" xml:space="preserve">
    <value>إضافة منتج</value>
  </data>
  <data name="EditProduct" xml:space="preserve">
    <value>تعديل منتج</value>
  </data>
  <data name="DeleteProduct" xml:space="preserve">
    <value>حذف منتج</value>
  </data>
  <data name="AvailableQuantity" xml:space="preserve">
    <value>الكمية المتاحة</value>
  </data>
  <data name="UnitCost" xml:space="preserve">
    <value>تكلفة الوحدة</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>العلامة التجارية</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>الموديل</value>
  </data>
  <data name="MinStockLevel" xml:space="preserve">
    <value>الحد الأدنى للمخزون</value>
  </data>
  
  <!-- Reports -->
  <data name="DailyReport" xml:space="preserve">
    <value>تقرير يومي</value>
  </data>
  <data name="MonthlyReport" xml:space="preserve">
    <value>تقرير شهري</value>
  </data>
  <data name="TotalRevenue" xml:space="preserve">
    <value>إجمالي الإيرادات</value>
  </data>
  <data name="TotalProfit" xml:space="preserve">
    <value>إجمالي الربح</value>
  </data>
  <data name="TopSellingProducts" xml:space="preserve">
    <value>المنتجات الأكثر مبيعاً</value>
  </data>
  
  <!-- Common -->
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>تصفية</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>نعم</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>موافق</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  
  <!-- Messages -->
  <data name="LoginSuccessful" xml:space="preserve">
    <value>تم تسجيل الدخول بنجاح</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>فشل تسجيل الدخول</value>
  </data>
  <data name="InvalidCredentials" xml:space="preserve">
    <value>اسم المستخدم أو كلمة المرور غير صحيحة</value>
  </data>
  <data name="SaleCompleted" xml:space="preserve">
    <value>تمت عملية البيع بنجاح</value>
  </data>
  <data name="ProductAdded" xml:space="preserve">
    <value>تم إضافة المنتج بنجاح</value>
  </data>
  <data name="ProductUpdated" xml:space="preserve">
    <value>تم تحديث المنتج بنجاح</value>
  </data>
  <data name="ProductDeleted" xml:space="preserve">
    <value>تم حذف المنتج بنجاح</value>
  </data>
  <data name="BackupCompleted" xml:space="preserve">
    <value>تم إنشاء النسخة الاحتياطية بنجاح</value>
  </data>
  <data name="BackupFailed" xml:space="preserve">
    <value>فشل في إنشاء النسخة الاحتياطية</value>
  </data>
  <data name="LowStockWarning" xml:space="preserve">
    <value>تحذير: المخزون منخفض</value>
  </data>
  <data name="OutOfStock" xml:space="preserve">
    <value>نفد المخزون</value>
  </data>
  
</root>
