using System;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace MobileShopManagement.DataAccess
{
    /// <summary>
    /// فئة الاتصال بقاعدة البيانات - Database Connection Class
    /// تدير الاتصال بقاعدة البيانات وتوفر العمليات الأساسية
    /// </summary>
    public class DatabaseConnection : IDisposable
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseConnection> _logger;
        private SqlConnection? _connection;
        private bool _disposed = false;

        /// <summary>
        /// منشئ فئة الاتصال بقاعدة البيانات - Database Connection Constructor
        /// </summary>
        /// <param name="configuration">إعدادات التطبيق</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DatabaseConnection(IConfiguration configuration, ILogger<DatabaseConnection> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration), "سلسلة الاتصال غير موجودة");
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// منشئ فئة الاتصال بقاعدة البيانات مع سلسلة اتصال مخصصة
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DatabaseConnection(string connectionString, ILogger<DatabaseConnection> logger)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// الحصول على اتصال قاعدة البيانات - Get Database Connection
        /// </summary>
        /// <returns>اتصال قاعدة البيانات</returns>
        public SqlConnection GetConnection()
        {
            try
            {
                if (_connection == null)
                {
                    _connection = new SqlConnection(_connectionString);
                }

                if (_connection.State == ConnectionState.Closed)
                {
                    _connection.Open();
                    _logger.LogInformation("تم فتح الاتصال بقاعدة البيانات بنجاح");
                }

                return _connection;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "خطأ في الاتصال بقاعدة البيانات: {Message}", ex.Message);
                throw new DatabaseConnectionException("فشل في الاتصال بقاعدة البيانات", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع في الاتصال بقاعدة البيانات: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات - Test Database Connection
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجح</returns>
        public bool TestConnection()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                connection.Open();
                _logger.LogInformation("اختبار الاتصال بقاعدة البيانات نجح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "فشل اختبار الاتصال بقاعدة البيانات: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع النتائج - Execute Query and Return Results
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>جدول البيانات</returns>
        public DataTable ExecuteQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                connection.Open();
                using var command = new SqlCommand(query, connection);

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);

                _logger.LogInformation("تم تنفيذ الاستعلام بنجاح، عدد الصفوف: {RowCount}", dataTable.Rows.Count);
                return dataTable;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام: {Query}, الخطأ: {Message}", query, ex.Message);
                throw new DatabaseOperationException("فشل في تنفيذ الاستعلام", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر بدون إرجاع نتائج - Execute Non-Query Command
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                connection.Open();
                using var command = new SqlCommand(query, connection);

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                int rowsAffected = command.ExecuteNonQuery();
                _logger.LogInformation("تم تنفيذ الأمر بنجاح، عدد الصفوف المتأثرة: {RowsAffected}", rowsAffected);
                return rowsAffected;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الأمر: {Query}, الخطأ: {Message}", query, ex.Message);
                throw new DatabaseOperationException("فشل في تنفيذ الأمر", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة - Execute Scalar Command
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public object? ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                connection.Open();
                using var command = new SqlCommand(query, connection);

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = command.ExecuteScalar();
                _logger.LogInformation("تم تنفيذ الأمر Scalar بنجاح");
                return result;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الأمر Scalar: {Query}, الخطأ: {Message}", query, ex.Message);
                throw new DatabaseOperationException("فشل في تنفيذ الأمر Scalar", ex);
            }
        }

        /// <summary>
        /// بدء معاملة قاعدة البيانات - Begin Database Transaction
        /// </summary>
        /// <returns>معاملة قاعدة البيانات</returns>
        public SqlTransaction BeginTransaction()
        {
            try
            {
                var connection = GetConnection();
                var transaction = connection.BeginTransaction();
                _logger.LogInformation("تم بدء معاملة قاعدة البيانات");
                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في بدء معاملة قاعدة البيانات: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// إغلاق الاتصال - Close Connection
        /// </summary>
        public void CloseConnection()
        {
            try
            {
                if (_connection != null && _connection.State != ConnectionState.Closed)
                {
                    _connection.Close();
                    _logger.LogInformation("تم إغلاق الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إغلاق الاتصال: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// تحرير الموارد - Dispose Resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تحرير الموارد المدارة وغير المدارة - Dispose Managed and Unmanaged Resources
        /// </summary>
        /// <param name="disposing">تحرير الموارد المدارة</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    CloseConnection();
                    _connection?.Dispose();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// المدمر - Finalizer
        /// </summary>
        ~DatabaseConnection()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// استثناء اتصال قاعدة البيانات - Database Connection Exception
    /// </summary>
    public class DatabaseConnectionException : Exception
    {
        public DatabaseConnectionException(string message) : base(message) { }
        public DatabaseConnectionException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// استثناء عملية قاعدة البيانات - Database Operation Exception
    /// </summary>
    public class DatabaseOperationException : Exception
    {
        public DatabaseOperationException(string message) : base(message) { }
        public DatabaseOperationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
