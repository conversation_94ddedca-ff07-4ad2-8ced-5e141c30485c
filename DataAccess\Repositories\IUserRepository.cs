using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MobileShopManagement.Models;

namespace MobileShopManagement.DataAccess.Repositories
{
    /// <summary>
    /// واجهة مستودع المستخدمين - User Repository Interface
    /// تحدد العمليات المتاحة لإدارة المستخدمين
    /// </summary>
    public interface IUserRepository
    {
        /// <summary>
        /// الحصول على جميع المستخدمين - Get All Users
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        Task<IEnumerable<User>> GetAllUsersAsync();

        /// <summary>
        /// الحصول على مستخدم بالمعرف - Get User by ID
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        Task<User?> GetUserByIdAsync(int userId);

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم - Get User by Username
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// إضافة مستخدم جديد - Add New User
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>معرف المستخدم الجديد</returns>
        Task<int> AddUserAsync(User user);

        /// <summary>
        /// تحديث بيانات المستخدم - Update User
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        Task<bool> UpdateUserAsync(User user);

        /// <summary>
        /// حذف مستخدم - Delete User
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> DeleteUserAsync(int userId);

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول - Update Last Login Date
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        Task<bool> UpdateLastLoginAsync(int userId);

        /// <summary>
        /// التحقق من وجود اسم المستخدم - Check if Username Exists
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="excludeUserId">معرف المستخدم المستثنى من البحث</param>
        /// <returns>true إذا كان اسم المستخدم موجود</returns>
        Task<bool> UsernameExistsAsync(string username, int? excludeUserId = null);

        /// <summary>
        /// الحصول على المستخدمين النشطين - Get Active Users
        /// </summary>
        /// <returns>قائمة المستخدمين النشطين</returns>
        Task<IEnumerable<User>> GetActiveUsersAsync();

        /// <summary>
        /// الحصول على المستخدمين حسب الدور - Get Users by Role
        /// </summary>
        /// <param name="role">الدور</param>
        /// <returns>قائمة المستخدمين</returns>
        Task<IEnumerable<User>> GetUsersByRoleAsync(string role);

        /// <summary>
        /// تغيير حالة المستخدم - Change User Status
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="isActive">الحالة الجديدة</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        Task<bool> ChangeUserStatusAsync(int userId, bool isActive);

        /// <summary>
        /// تغيير كلمة مرور المستخدم - Change User Password
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPasswordHash">كلمة المرور الجديدة المشفرة</param>
        /// <param name="newSalt">الملح الجديد</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        Task<bool> ChangePasswordAsync(int userId, string newPasswordHash, string newSalt);

        /// <summary>
        /// الحصول على عدد المستخدمين - Get User Count
        /// </summary>
        /// <returns>عدد المستخدمين</returns>
        Task<int> GetUserCountAsync();

        /// <summary>
        /// البحث في المستخدمين - Search Users
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة المستخدمين المطابقة</returns>
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);
    }
}
