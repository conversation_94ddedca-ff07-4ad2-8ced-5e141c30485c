using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MobileShopManagement.Models;

namespace MobileShopManagement.DataAccess.Repositories
{
    /// <summary>
    /// مستودع المستخدمين - User Repository Implementation
    /// يوفر العمليات الخاصة بإدارة المستخدمين في قاعدة البيانات
    /// </summary>
    public class UserRepository : IUserRepository
    {
        private readonly DatabaseConnection _dbConnection;
        private readonly ILogger<UserRepository> _logger;

        /// <summary>
        /// منشئ مستودع المستخدمين - User Repository Constructor
        /// </summary>
        /// <param name="dbConnection">اتصال قاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserRepository(DatabaseConnection dbConnection, ILogger<UserRepository> logger)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// الحصول على جميع المستخدمين - Get All Users
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName, 
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users 
                    ORDER BY FullName";

                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query));
                return MapDataTableToUsers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف - Get User by ID
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        public async Task<User?> GetUserByIdAsync(int userId)
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName, 
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users 
                    WHERE UserID = @UserID";

                var parameters = new[] { new SqlParameter("@UserID", userId) };
                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query, parameters));

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToUser(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم بالمعرف: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم - Get User by Username
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName, 
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users 
                    WHERE Username = @Username";

                var parameters = new[] { new SqlParameter("@Username", username) };
                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query, parameters));

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToUser(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم باسم المستخدم: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// إضافة مستخدم جديد - Add New User
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public async Task<int> AddUserAsync(User user)
        {
            try
            {
                const string query = @"
                    INSERT INTO Users (Username, PasswordHash, Salt, Role, FullName, IsActive, CreatedDate)
                    VALUES (@Username, @PasswordHash, @Salt, @Role, @FullName, @IsActive, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@Username", user.Username),
                    new SqlParameter("@PasswordHash", user.PasswordHash),
                    new SqlParameter("@Salt", user.Salt),
                    new SqlParameter("@Role", user.Role),
                    new SqlParameter("@FullName", user.FullName),
                    new SqlParameter("@IsActive", user.IsActive),
                    new SqlParameter("@CreatedDate", user.CreatedDate)
                };

                var result = await Task.Run(() => _dbConnection.ExecuteScalar(query, parameters));
                var userId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة مستخدم جديد بنجاح: {Username}, المعرف: {UserId}", user.Username, userId);
                return userId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة مستخدم جديد: {Username}", user.Username);
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم - Update User
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                const string query = @"
                    UPDATE Users 
                    SET Username = @Username, Role = @Role, FullName = @FullName, IsActive = @IsActive
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    new SqlParameter("@UserID", user.UserID),
                    new SqlParameter("@Username", user.Username),
                    new SqlParameter("@Role", user.Role),
                    new SqlParameter("@FullName", user.FullName),
                    new SqlParameter("@IsActive", user.IsActive)
                };

                var rowsAffected = await Task.Run(() => _dbConnection.ExecuteNonQuery(query, parameters));
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث بيانات المستخدم بنجاح: {Username}", user.Username);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات المستخدم: {Username}", user.Username);
                throw;
            }
        }

        /// <summary>
        /// حذف مستخدم - Delete User
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                const string query = "DELETE FROM Users WHERE UserID = @UserID";
                var parameters = new[] { new SqlParameter("@UserID", userId) };

                var rowsAffected = await Task.Run(() => _dbConnection.ExecuteNonQuery(query, parameters));
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف المستخدم بنجاح: {UserId}", userId);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول - Update Last Login Date
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateLastLoginAsync(int userId)
        {
            try
            {
                const string query = @"
                    UPDATE Users 
                    SET LastLoginDate = @LastLoginDate 
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    new SqlParameter("@UserID", userId),
                    new SqlParameter("@LastLoginDate", DateTime.Now)
                };

                var rowsAffected = await Task.Run(() => _dbConnection.ExecuteNonQuery(query, parameters));
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث تاريخ آخر تسجيل دخول: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم - Check if Username Exists
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="excludeUserId">معرف المستخدم المستثنى من البحث</param>
        /// <returns>true إذا كان اسم المستخدم موجود</returns>
        public async Task<bool> UsernameExistsAsync(string username, int? excludeUserId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
                var parameters = new List<SqlParameter> { new("@Username", username) };

                if (excludeUserId.HasValue)
                {
                    query += " AND UserID != @ExcludeUserId";
                    parameters.Add(new SqlParameter("@ExcludeUserId", excludeUserId.Value));
                }

                var result = await Task.Run(() => _dbConnection.ExecuteScalar(query, parameters.ToArray()));
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود اسم المستخدم: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين النشطين - Get Active Users
        /// </summary>
        /// <returns>قائمة المستخدمين النشطين</returns>
        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName,
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users
                    WHERE IsActive = 1
                    ORDER BY FullName";

                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query));
                return MapDataTableToUsers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدمين النشطين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين حسب الدور - Get Users by Role
        /// </summary>
        /// <param name="role">الدور</param>
        /// <returns>قائمة المستخدمين</returns>
        public async Task<IEnumerable<User>> GetUsersByRoleAsync(string role)
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName,
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users
                    WHERE Role = @Role
                    ORDER BY FullName";

                var parameters = new[] { new SqlParameter("@Role", role) };
                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query, parameters));
                return MapDataTableToUsers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدمين حسب الدور: {Role}", role);
                throw;
            }
        }

        /// <summary>
        /// تغيير حالة المستخدم - Change User Status
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="isActive">الحالة الجديدة</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        public async Task<bool> ChangeUserStatusAsync(int userId, bool isActive)
        {
            try
            {
                const string query = "UPDATE Users SET IsActive = @IsActive WHERE UserID = @UserID";
                var parameters = new[]
                {
                    new SqlParameter("@UserID", userId),
                    new SqlParameter("@IsActive", isActive)
                };

                var rowsAffected = await Task.Run(() => _dbConnection.ExecuteNonQuery(query, parameters));
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم - Change User Password
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPasswordHash">كلمة المرور الجديدة المشفرة</param>
        /// <param name="newSalt">الملح الجديد</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        public async Task<bool> ChangePasswordAsync(int userId, string newPasswordHash, string newSalt)
        {
            try
            {
                const string query = @"
                    UPDATE Users
                    SET PasswordHash = @PasswordHash, Salt = @Salt
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    new SqlParameter("@UserID", userId),
                    new SqlParameter("@PasswordHash", newPasswordHash),
                    new SqlParameter("@Salt", newSalt)
                };

                var rowsAffected = await Task.Run(() => _dbConnection.ExecuteNonQuery(query, parameters));
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة مرور المستخدم: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على عدد المستخدمين - Get User Count
        /// </summary>
        /// <returns>عدد المستخدمين</returns>
        public async Task<int> GetUserCountAsync()
        {
            try
            {
                const string query = "SELECT COUNT(*) FROM Users";
                var result = await Task.Run(() => _dbConnection.ExecuteScalar(query));
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على عدد المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// البحث في المستخدمين - Search Users
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة المستخدمين المطابقة</returns>
        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            try
            {
                const string query = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, FullName,
                           IsActive, CreatedDate, LastLoginDate
                    FROM Users
                    WHERE Username LIKE @SearchTerm OR FullName LIKE @SearchTerm
                    ORDER BY FullName";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await Task.Run(() => _dbConnection.ExecuteQuery(query, parameters));
                return MapDataTableToUsers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في المستخدمين: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة مستخدمين - Map DataTable to Users
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة المستخدمين</returns>
        private static IEnumerable<User> MapDataTableToUsers(DataTable dataTable)
        {
            var users = new List<User>();
            foreach (DataRow row in dataTable.Rows)
            {
                users.Add(MapDataRowToUser(row));
            }
            return users;
        }

        /// <summary>
        /// تحويل DataRow إلى مستخدم - Map DataRow to User
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>المستخدم</returns>
        private static User MapDataRowToUser(DataRow row)
        {
            return new User
            {
                UserID = Convert.ToInt32(row["UserID"]),
                Username = row["Username"].ToString() ?? string.Empty,
                PasswordHash = row["PasswordHash"].ToString() ?? string.Empty,
                Salt = row["Salt"].ToString() ?? string.Empty,
                Role = row["Role"].ToString() ?? string.Empty,
                FullName = row["FullName"].ToString() ?? string.Empty,
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastLoginDate = row["LastLoginDate"] == DBNull.Value ? null : Convert.ToDateTime(row["LastLoginDate"])
            };
        }
    }
}
