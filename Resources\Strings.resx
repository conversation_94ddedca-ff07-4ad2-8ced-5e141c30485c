<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Login Form -->
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="LoginButton" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Remember Me</value>
  </data>
  
  <!-- Main Form -->
  <data name="MainTitle" xml:space="preserve">
    <value>Mobile Shop Management System</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Backup" xml:space="preserve">
    <value>Backup</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  
  <!-- Sales Form -->
  <data name="NewSale" xml:space="preserve">
    <value>New Sale</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="SalePrice" xml:space="preserve">
    <value>Sale Price</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="CustomerPhone" xml:space="preserve">
    <value>Customer Phone</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Payment Method</value>
  </data>
  <data name="Cash" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="Card" xml:space="preserve">
    <value>Card</value>
  </data>
  <data name="Transfer" xml:space="preserve">
    <value>Transfer</value>
  </data>
  <data name="Installment" xml:space="preserve">
    <value>Installment</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  
  <!-- Inventory Form -->
  <data name="AddProduct" xml:space="preserve">
    <value>Add Product</value>
  </data>
  <data name="EditProduct" xml:space="preserve">
    <value>Edit Product</value>
  </data>
  <data name="DeleteProduct" xml:space="preserve">
    <value>Delete Product</value>
  </data>
  <data name="AvailableQuantity" xml:space="preserve">
    <value>Available Quantity</value>
  </data>
  <data name="UnitCost" xml:space="preserve">
    <value>Unit Cost</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>Selling Price</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="MinStockLevel" xml:space="preserve">
    <value>Min Stock Level</value>
  </data>
  
  <!-- Reports -->
  <data name="DailyReport" xml:space="preserve">
    <value>Daily Report</value>
  </data>
  <data name="MonthlyReport" xml:space="preserve">
    <value>Monthly Report</value>
  </data>
  <data name="TotalRevenue" xml:space="preserve">
    <value>Total Revenue</value>
  </data>
  <data name="TotalProfit" xml:space="preserve">
    <value>Total Profit</value>
  </data>
  <data name="TopSellingProducts" xml:space="preserve">
    <value>Top Selling Products</value>
  </data>
  
  <!-- Common -->
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  
  <!-- Messages -->
  <data name="LoginSuccessful" xml:space="preserve">
    <value>Login successful</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>Login failed</value>
  </data>
  <data name="InvalidCredentials" xml:space="preserve">
    <value>Invalid username or password</value>
  </data>
  <data name="SaleCompleted" xml:space="preserve">
    <value>Sale completed successfully</value>
  </data>
  <data name="ProductAdded" xml:space="preserve">
    <value>Product added successfully</value>
  </data>
  <data name="ProductUpdated" xml:space="preserve">
    <value>Product updated successfully</value>
  </data>
  <data name="ProductDeleted" xml:space="preserve">
    <value>Product deleted successfully</value>
  </data>
  <data name="BackupCompleted" xml:space="preserve">
    <value>Backup completed successfully</value>
  </data>
  <data name="BackupFailed" xml:space="preserve">
    <value>Backup failed</value>
  </data>
  <data name="LowStockWarning" xml:space="preserve">
    <value>Low stock warning</value>
  </data>
  <data name="OutOfStock" xml:space="preserve">
    <value>Out of stock</value>
  </data>
  
</root>
