using System;
using System.ComponentModel.DataAnnotations;

namespace MobileShopManagement.Models
{
    /// <summary>
    /// نموذج المستخدم - User Model
    /// يمثل بيانات المستخدمين في النظام
    /// </summary>
    public class User
    {
        /// <summary>
        /// معرف المستخدم الفريد - Unique User ID
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// اسم المستخدم - Username
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور المشفرة - Hashed Password
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// الملح المستخدم في التشفير - Salt for hashing
        /// </summary>
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// دور المستخدم في النظام - User Role
        /// </summary>
        [Required(ErrorMessage = "دور المستخدم مطلوب")]
        public string Role { get; set; } = "Employee";

        /// <summary>
        /// الاسم الكامل للمستخدم - Full Name
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// حالة المستخدم (نشط/غير نشط) - User Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الحساب - Account Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تسجيل دخول - Last Login Date
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// التحقق من صحة دور المستخدم - Validate User Role
        /// </summary>
        /// <returns>true إذا كان الدور صحيح</returns>
        public bool IsValidRole()
        {
            return Role == "Admin" || Role == "Manager" || Role == "Employee";
        }

        /// <summary>
        /// التحقق من صلاحيات الإدارة - Check Admin Permissions
        /// </summary>
        /// <returns>true إذا كان المستخدم مدير</returns>
        public bool IsAdmin()
        {
            return Role == "Admin";
        }

        /// <summary>
        /// التحقق من صلاحيات المدير - Check Manager Permissions
        /// </summary>
        /// <returns>true إذا كان المستخدم مدير أو مدير عام</returns>
        public bool IsManagerOrAbove()
        {
            return Role == "Admin" || Role == "Manager";
        }

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول - Update Last Login Date
        /// </summary>
        public void UpdateLastLogin()
        {
            LastLoginDate = DateTime.Now;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للمستخدم - String representation
        /// </summary>
        /// <returns>اسم المستخدم والدور</returns>
        public override string ToString()
        {
            return $"{FullName} ({Username}) - {Role}";
        }
    }

    /// <summary>
    /// أدوار المستخدمين في النظام - User Roles Enumeration
    /// </summary>
    public enum UserRole
    {
        /// <summary>موظف - Employee</summary>
        Employee,
        /// <summary>مدير - Manager</summary>
        Manager,
        /// <summary>مدير عام - Administrator</summary>
        Admin
    }

    /// <summary>
    /// نموذج تسجيل الدخول - Login Model
    /// </summary>
    public class LoginModel
    {
        /// <summary>
        /// اسم المستخدم - Username
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور - Password
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// تذكر تسجيل الدخول - Remember Login
        /// </summary>
        public bool RememberMe { get; set; } = false;
    }
}
