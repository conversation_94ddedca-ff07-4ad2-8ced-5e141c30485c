# نظام إدارة محل الهواتف المحمولة - System Architecture & Implementation Guide

## 🏗️ معمارية النظام - System Architecture

### 1. نمط الطبقات الثلاث - 3-Tier Architecture

```
┌─────────────────────────────────────┐
│        Presentation Layer          │  ← Windows Forms UI
│     (UI Forms & User Controls)     │
├─────────────────────────────────────┤
│        Business Logic Layer        │  ← Services & Business Rules
│    (Authentication, Sales, etc.)   │
├─────────────────────────────────────┤
│       Data Access Layer            │  ← Repository Pattern
│    (Database Operations & DAL)     │
└─────────────────────────────────────┘
```

### 2. قاعدة البيانات - Database Schema

#### الجداول الرئيسية - Main Tables:

**Users Table** - جدول المستخدمين
```sql
- UserID (PK, Identity)
- Username (Unique, nvarchar(50))
- PasswordHash (nvarchar(255))
- Salt (nvarchar(255))
- Role (nvarchar(20): Admin/Manager/Employee)
- FullName (nvarchar(100))
- IsActive (bit)
- CreatedDate (datetime2)
- LastLoginDate (datetime2)
```

**Inventory Table** - جدول المخزون
```sql
- ProductID (PK, Identity)
- ProductName (nvarchar(100))
- ProductNameArabic (nvarchar(100))
- QuantityAvailable (int)
- UnitCost (decimal(10,2))
- SellingPrice (decimal(10,2))
- Category (nvarchar(50))
- CategoryArabic (nvarchar(50))
- Brand (nvarchar(50))
- Model (nvarchar(50))
- MinStockLevel (int)
- IsActive (bit)
- CreatedDate (datetime2)
- LastUpdated (datetime2)
```

**Sales Table** - جدول المبيعات
```sql
- SaleID (PK, Identity)
- ProductID (FK to Inventory)
- ProductName (nvarchar(100))
- SalePrice (decimal(10,2))
- UnitCost (decimal(10,2))
- QuantitySold (int)
- TotalAmount (Computed: SalePrice * QuantitySold)
- Profit (Computed: (SalePrice - UnitCost) * QuantitySold)
- CustomerName (nvarchar(100))
- CustomerPhone (nvarchar(20))
- SaleDate (datetime2)
- SaleDay (Computed: DATENAME(WEEKDAY, SaleDate))
- SoldBy (FK to Users)
- PaymentMethod (nvarchar(20))
- Notes (nvarchar(500))
```

## 🔧 التقنيات المستخدمة - Technologies Used

### Frontend
- **Windows Forms** - واجهة المستخدم الرئيسية
- **Custom Controls** - عناصر مخصصة للواجهة العربية
- **Resource Files (.resx)** - ملفات الترجمة والتوطين

### Backend
- **.NET 6** - إطار العمل الأساسي
- **C# 10** - لغة البرمجة
- **ADO.NET** - للوصول إلى قاعدة البيانات
- **Repository Pattern** - نمط تصميم للبيانات
- **Dependency Injection** - حقن التبعيات

### Database
- **SQL Server 2019+** - قاعدة البيانات الرئيسية
- **SQL Server Express** - للبيئات الصغيرة
- **LocalDB** - للتطوير والاختبار

### Security
- **BCrypt.Net** - تشفير كلمات المرور
- **Salt Hashing** - تأمين إضافي للكلمات
- **Role-Based Access** - التحكم في الصلاحيات

### Reporting & Export
- **ClosedXML** - تصدير Excel
- **PrintDocument** - طباعة التقارير
- **Crystal Reports** (اختياري) - تقارير متقدمة

### Logging & Monitoring
- **Serilog** - تسجيل الأحداث
- **Microsoft.Extensions.Logging** - إطار التسجيل
- **File & Console Sinks** - وجهات التسجيل

## 📁 هيكل المشروع التفصيلي - Detailed Project Structure

```
MobileShopManagement/
├── 📁 BusinessLogic/
│   ├── AuthenticationService.cs      # خدمة المصادقة
│   ├── SalesService.cs              # خدمة المبيعات
│   ├── InventoryService.cs          # خدمة المخزون
│   ├── ReportService.cs             # خدمة التقارير
│   ├── BackupService.cs             # خدمة النسخ الاحتياطي
│   └── UserService.cs               # خدمة إدارة المستخدمين
├── 📁 DataAccess/
│   ├── DatabaseConnection.cs        # اتصال قاعدة البيانات
│   ├── 📁 Repositories/
│   │   ├── IUserRepository.cs       # واجهة مستودع المستخدمين
│   │   ├── UserRepository.cs        # تنفيذ مستودع المستخدمين
│   │   ├── IProductRepository.cs    # واجهة مستودع المنتجات
│   │   ├── ProductRepository.cs     # تنفيذ مستودع المنتجات
│   │   ├── ISalesRepository.cs      # واجهة مستودع المبيعات
│   │   └── SalesRepository.cs       # تنفيذ مستودع المبيعات
├── 📁 Models/
│   ├── User.cs                      # نموذج المستخدم
│   ├── Product.cs                   # نموذج المنتج
│   ├── Sale.cs                      # نموذج البيع
│   ├── Customer.cs                  # نموذج العميل
│   └── BackupLog.cs                 # نموذج سجل النسخ الاحتياطي
├── 📁 UI/
│   ├── 📁 Forms/
│   │   ├── LoginForm.cs             # نموذج تسجيل الدخول
│   │   ├── MainForm.cs              # النموذج الرئيسي
│   │   ├── SalesForm.cs             # نموذج المبيعات
│   │   ├── InventoryForm.cs         # نموذج المخزون
│   │   ├── ReportsForm.cs           # نموذج التقارير
│   │   ├── UsersForm.cs             # نموذج إدارة المستخدمين
│   │   └── BackupForm.cs            # نموذج النسخ الاحتياطي
│   └── 📁 UserControls/
│       ├── ProductCard.cs           # بطاقة المنتج
│       ├── SalesSummary.cs          # ملخص المبيعات
│       └── StockAlert.cs            # تنبيه المخزون
├── 📁 Resources/
│   ├── Strings.resx                 # النصوص الافتراضية
│   ├── Strings.ar.resx              # النصوص العربية
│   ├── Strings.en.resx              # النصوص الإنجليزية
│   └── 📁 Images/                   # الصور والأيقونات
├── 📁 Utilities/
│   ├── LocalizationManager.cs       # مدير الترجمة
│   ├── ExcelExporter.cs             # مصدر Excel
│   ├── PrintManager.cs              # مدير الطباعة
│   ├── ValidationHelper.cs          # مساعد التحقق
│   └── DateTimeHelper.cs            # مساعد التاريخ والوقت
├── 📁 Reports/
│   ├── DailyReport.cs               # التقرير اليومي
│   ├── MonthlyReport.cs             # التقرير الشهري
│   └── InventoryReport.cs           # تقرير المخزون
├── 📁 Backup/
│   ├── DatabaseBackup.cs            # نسخ احتياطي لقاعدة البيانات
│   └── FileBackup.cs                # نسخ احتياطي للملفات
├── 📁 Database/
│   ├── CreateDatabase.sql           # إنشاء قاعدة البيانات
│   ├── SampleData.sql               # بيانات تجريبية
│   └── StoredProcedures.sql         # الإجراءات المخزنة
├── Program.cs                       # نقطة دخول التطبيق
├── appsettings.json                 # إعدادات التطبيق
└── MobileShopManagement.csproj      # ملف المشروع
```

## 🔐 نظام الأمان - Security Implementation

### 1. تشفير كلمات المرور
```csharp
// استخدام BCrypt مع Salt عشوائي
public static (string hash, string salt) HashPassword(string password)
{
    var salt = BCrypt.Net.BCrypt.GenerateSalt(12);
    var hash = BCrypt.Net.BCrypt.HashPassword(password, salt);
    return (hash, salt);
}
```

### 2. إدارة الجلسات
```csharp
public class SessionManager
{
    private static User? _currentUser;
    private static DateTime _lastActivity;
    private static readonly TimeSpan SessionTimeout = TimeSpan.FromHours(8);
    
    public static bool IsSessionValid => 
        _currentUser != null && 
        DateTime.Now - _lastActivity < SessionTimeout;
}
```

### 3. التحكم في الصلاحيات
```csharp
public enum UserRole { Employee, Manager, Admin }

public bool HasPermission(string action, UserRole userRole)
{
    return action switch
    {
        "ViewSales" => userRole >= UserRole.Employee,
        "ManageInventory" => userRole >= UserRole.Manager,
        "ManageUsers" => userRole >= UserRole.Admin,
        _ => false
    };
}
```

## 🌐 نظام الترجمة والتوطين - Localization System

### 1. مدير الترجمة
```csharp
public static class LocalizationManager
{
    private static CultureInfo _currentCulture = new("ar-SA");
    private static ResourceManager _resourceManager;
    
    public static string GetString(string key) =>
        _resourceManager.GetString(key, _currentCulture) ?? key;
        
    public static void SetCulture(string cultureName)
    {
        _currentCulture = new CultureInfo(cultureName);
        Thread.CurrentThread.CurrentUICulture = _currentCulture;
    }
}
```

### 2. تطبيق RTL Layout
```csharp
public static void ApplyRTL(Form form)
{
    form.RightToLeft = RightToLeft.Yes;
    form.RightToLeftLayout = true;
    
    foreach (Control control in form.Controls)
    {
        if (control is TextBox textBox)
            textBox.TextAlign = HorizontalAlignment.Right;
    }
}
```

## 📊 نظام التقارير - Reporting System

### 1. تقرير المبيعات اليومي
```csharp
public class DailyReport
{
    public DateTime Date { get; set; }
    public decimal TotalSales { get; set; }
    public decimal TotalProfit { get; set; }
    public int TransactionCount { get; set; }
    public List<TopProduct> TopProducts { get; set; }
}
```

### 2. تصدير Excel
```csharp
public void ExportToExcel(List<Sale> sales, string filePath)
{
    using var workbook = new XLWorkbook();
    var worksheet = workbook.Worksheets.Add("المبيعات");
    
    // إضافة العناوين
    worksheet.Cell(1, 1).Value = "رقم البيع";
    worksheet.Cell(1, 2).Value = "اسم المنتج";
    // ... المزيد من العناوين
    
    // إضافة البيانات
    for (int i = 0; i < sales.Count; i++)
    {
        worksheet.Cell(i + 2, 1).Value = sales[i].SaleID;
        worksheet.Cell(i + 2, 2).Value = sales[i].ProductName;
        // ... المزيد من البيانات
    }
    
    workbook.SaveAs(filePath);
}
```

## 💾 نظام النسخ الاحتياطي - Backup System

### 1. النسخ الاحتياطي التلقائي
```csharp
public class AutoBackupService
{
    private readonly Timer _timer;
    
    public AutoBackupService()
    {
        _timer = new Timer(TimeSpan.FromHours(24).TotalMilliseconds);
        _timer.Elapsed += PerformBackup;
        _timer.Start();
    }
    
    private void PerformBackup(object sender, ElapsedEventArgs e)
    {
        var backupPath = GenerateBackupPath();
        BackupDatabase(backupPath);
        CompressBackup(backupPath);
        CleanOldBackups();
    }
}
```

### 2. ضغط النسخ الاحتياطية
```csharp
public void CompressBackup(string backupPath)
{
    var zipPath = backupPath.Replace(".bak", ".zip");
    using var archive = ZipFile.Open(zipPath, ZipArchiveMode.Create);
    archive.CreateEntryFromFile(backupPath, Path.GetFileName(backupPath));
    File.Delete(backupPath); // حذف الملف الأصلي
}
```

## 🚀 خطة التنفيذ - Implementation Plan

### المرحلة 1: الأساسيات (أسبوع 1-2)
- [x] إعداد قاعدة البيانات
- [x] نظام المصادقة
- [x] الواجهة الأساسية
- [x] نظام الترجمة

### المرحلة 2: المبيعات والمخزون (أسبوع 3-4)
- [ ] إدارة المنتجات
- [ ] تسجيل المبيعات
- [ ] تحديث المخزون
- [ ] تنبيهات المخزون المنخفض

### المرحلة 3: التقارير والطباعة (أسبوع 5-6)
- [ ] التقارير اليومية والشهرية
- [ ] تصدير Excel
- [ ] طباعة الفواتير
- [ ] تحليل الأرباح

### المرحلة 4: النسخ الاحتياطي والأمان (أسبوع 7-8)
- [ ] النسخ الاحتياطي اليدوي والتلقائي
- [ ] تشفير البيانات الحساسة
- [ ] سجلات التدقيق
- [ ] إدارة المستخدمين المتقدمة

### المرحلة 5: التحسينات والاختبار (أسبوع 9-10)
- [ ] تحسين الأداء
- [ ] اختبار شامل
- [ ] إصلاح الأخطاء
- [ ] توثيق المستخدم

## 📝 ملاحظات التطوير - Development Notes

### أفضل الممارسات
1. **استخدام async/await** لجميع عمليات قاعدة البيانات
2. **معالجة الأخطاء الشاملة** مع تسجيل مفصل
3. **التحقق من صحة البيانات** في جميع المستويات
4. **اختبار الوحدة** لجميع الخدمات الأساسية
5. **توثيق XML** لجميع الفئات والطرق العامة

### اعتبارات الأداء
1. **فهرسة قاعدة البيانات** للاستعلامات المتكررة
2. **تخزين مؤقت** للبيانات المستخدمة بكثرة
3. **تحميل البيانات بالصفحات** للقوائم الكبيرة
4. **ضغط النسخ الاحتياطية** لتوفير المساحة

### الأمان
1. **تشفير جميع كلمات المرور** باستخدام BCrypt
2. **التحقق من الصلاحيات** في كل عملية
3. **تسجيل جميع العمليات الحساسة**
4. **انتهاء صلاحية الجلسات** التلقائي

---

**هذا النظام مصمم ليكون قابلاً للتوسع والصيانة مع التركيز على الأمان والأداء وسهولة الاستخدام.**
